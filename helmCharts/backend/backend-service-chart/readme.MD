How to install:

From the root directory of helm chart:

release=n-machine-maintenance
ns=stage-backend
values_file="values/${release}-values.yaml"


#To install
echo "helm install --debug $release -n $ns -f ${values_file} . 2>&1 | tee outputs/${release}-install.log"

#TO upgrade
echo "helm upgrade --wait $release -n $ns -f ${values_file} . 2>&1 | tee outputs/${release}-install.log"

#To uninstall the release:
echo "helm delete $release -n $ns"
echo "helm delete --purge $release -n $ns"
echo "helm uninstall $release -n $ns"