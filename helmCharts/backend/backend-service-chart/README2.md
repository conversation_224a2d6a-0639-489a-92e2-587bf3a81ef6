# EFS Heapdump Volume Configuration

This directory contains the Kubernetes manifests for configuring EFS-based volumes for Java heapdumps.

## Overview

The heapdump volume configuration provides:
- EFS-based persistent storage for Java heap dumps
- Pod-specific subdirectories within the shared EFS volume
- Automatic directory creation and permissions setup
- Integration with JVM heap dump configuration

## Files

- `efs-storageclass.yaml`: StorageClass for EFS CSI driver
- `heapdump-pv.yaml`: PersistentVolume definition for EFS
- `heapdump-pvc.yaml`: PersistentVolumeClaim for the heapdump volume

## Configuration

To enable heapdump EFS volume, add the following to your values.yaml:

```yaml
heapdump:
  enabled: true
  storage: "2Gi"
  storageClassName: "efs-sc"
  efsFileSystemId: "fs-xxxxxxxxx"  # Replace with actual EFS file system ID
  efsPath: "/heapdumps"  # Optional: subdirectory within EFS
```

## Prerequisites

1. **EFS File System**: Create an EFS file system in your AWS account
2. **EFS CSI Driver**: Install the AWS EFS CSI driver in your EKS cluster
3. **Security Groups**: Ensure proper security group rules for EFS access
4. **Mount Targets**: Create EFS mount targets in your VPC subnets

## Directory Structure

When enabled, the volume will be mounted at `/opt/heapdumps` with the following structure:
```
/opt/heapdumps/
├── <pod-name-1>/
├── <pod-name-2>/
└── <pod-name-n>/
```

Each pod gets its own subdirectory for heap dumps, preventing conflicts between pods.

## JVM Configuration

The JVM is automatically configured with:
- `-XX:+HeapDumpOnOutOfMemoryError`: Enable heap dumps on OOM
- `-XX:HeapDumpPath=/opt/heapdumps/${POD_NAME}`: Pod-specific dump location
- `-XX:+ExitOnOutOfMemoryError`: Exit after creating heap dump

## Notes

- The EFS volume supports ReadWriteMany access mode, allowing multiple pods to share the same volume
- Directory permissions are set to 777 to ensure the Java process can write heap dumps
- The volume uses the EFS CSI driver for dynamic provisioning
- Heap dumps are retained even if pods are restarted or rescheduled
