# Service-specific configuration for n-data-integrations
serviceName: "n-data-integrations"

# Override default debugging setting
scriptDebuggingEnabled: true


# Service-specific endpoint overrides
overrideServiceEndpoints:
  stage.kalpha.userside-web-id-external-host-url: "alpha-id.strawmine.com"
  stage.kalpha.unified-id-service-url: "http://alpha-unified-id.strawmine.com"
  stage.kalpha.unified-id-service-admin-external-url: "https://alpha-unified-id.strawmine.com"
  stage.kalpha.categories-service-url: "http://alpha-categories.strawmine.com"
  stage.kalpha.categories-service-admin-external-url: "https://alpha-categories.strawmine.com"
  stage.kalpha.n-live-data-streaming-external-host-url: "https:/kalpha-service-external.strawmine.com"

# Service-specific host aliases
hostAliases:
  - ip: "************"
    hostnames:
      - "alpha-unified-id.strawmine.com"

# Database configurations - service specific overrides
postgres:
  enabled: true
  cluster: "stage-nint-new.cyiz7opktgoc.ap-southeast-1.rds.amazonaws.com"

mongo:
  enabled: true
  mongoUri: "mongodb://************:27017,***********:27017,***********:27017/?replicaSet=eks-mongo-3"
  external:
    enabled: true
    ip: "************"
  dbToConnect: "admin"
  cluster: "************:27017,***********:27017,***********:27017"
  replicasetName: "eks-mongo-3"
  dbRoles:
    - role: "readWrite"
      db: "orders"
    - role: "read"
      db: "sales"

# Kafka configuration - service specific
kafka:
  enabled: true

# Analytic Kafka configuration - service specific
analyticKafka:
  enabled: false
  serviceName: "stage-nalpha-analytic-kafka-rest"
  namespace: "stage-analytic-kafka"
  restPort: 8090
  zookeeperPort: 2182
  brokerEndpoints:
    - "************"
    - "***********"
    - "***********"




# Service-specific ingress configuration
ingress:
  className: "external-nginx"
  http_context:
    context: "blz/core_platform_services"
    enabled: false
  blocks:
    - name: "root"
      location: "/"
      additionalAnnotations:
        nginx.ingress.kubernetes.io/proxy-connect-timeout: "3"
        nginx.ingress.kubernetes.io/proxy-next-upstream: "error timeout"
        nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
        nginx.ingress.kubernetes.io/client-max-body-size: "50m"
    - name: "admin"
      location: "/admin"
      additionalAnnotations:
        nginx.ingress.kubernetes.io/proxy-connect-timeout: "3"
        nginx.ingress.kubernetes.io/proxy-next-upstream: "error timeout"
        nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
        nginx.ingress.kubernetes.io/client-max-body-size: "50m"
  client_endpoint:
    - name: "n-input-app-web"
      host: "kalpha-n-input-app-web-external.strawmine.com"
      serviceIdentifier: "core_services"
      ext_enabled: false
      annotations: {}
    - name: "n-live-data-streaming"
      host: "kalpha-service-external.strawmine.com"
      serviceIdentifier: "core_services"
      ext_enabled: false
      annotations: {}
    - name: "n-live-data-streaming-2"
      host: "kalpha-service-external.strawmine.com"
      serviceIdentifier: "master_data_services"
      ext_enabled: false
      annotations: {}
  admin_endpoint:
    - name: "n-data-integrations endpoint"
      host: "kalpha-n-data-integrations.admin.strawmine.com"
      annotations: {}


# S3 configuration - service specific
s3:
  access:
    - bucketName: solvei8-stage-kalpha-n-machine-maintenance
      acl: write

# Init job configuration - service specific
initJob:
  enabled: true

# Service-specific deployment configuration
replicaCount: 2
image:
  repository: 185889327143.dkr.ecr.ap-southeast-1.amazonaws.com/zilingo-data-integrations-backend
  tag: "latest2"

# Service-specific probe configuration
livenessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true

readinessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  initialDelaySeconds: 10
  periodSeconds: 15

# Service-specific Java agent configuration
java:
  agents:
    - name: "jmx_prometheus_javaagent"
      path: "/opt/javaagents/jmx_prometheus_javaagent-0.20.0.jar=9050:/opt/service-confs/jmx_prometheus_config.yaml"
      options: ""
    - name: "opentelemetry_javaagent"
      path: "/opt/javaagents/opentelemetry-javaagent.jar"
      options: "-Dotel.resource.attributes=qualified.service.name=${QUALIFIED_SERVICE_NAME},host.name=$POD_NAME -Dotel.javaagent.debug=false -Dotel.exporter.otlp.protocol=grpc -Dotel.exporter.otlp.endpoint=http://alloy.observability-alloy.svc.cluster.local:4317 -Dotel.traces.exporter=otlp -Dotel.logs.exporter=otlp -Dotel.metric.export.interval=15000 -Dotel.metrics.exporter=otlp -Dotel.service.name=${QUALIFIED_SERVICE_NAME}"

# Service-specific application configuration
app:
  javaTmpDir: "/tmp/n-data-integrations"

# Service-specific JVM memory settings
jvm:
  memory:
    Xms: "256m"
    Xmx: "256m"

# Heapdump EFS volume configuration
heapdump:
  enabled: true
  efsFileSystemId: "fs-0f5d0ec509c876861"

# Service-specific Druid configuration
druid:
  coordinator:
    endpoints: [ "************" ]
  broker:
    endpoints: [ "***********" ]

# Service-specific Elasticsearch configuration
elasticsearch:
  external:
    enabled: true
    endpoints:
      - "************"
      - "***********"
      - "***********"
    IPs:
      - "************"
      - "***********"
      - "***********"

