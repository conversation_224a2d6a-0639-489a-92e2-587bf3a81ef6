#!/bin/bash

# Deployment script for backend services using the new values structure
# Usage: ./deploy.sh <service-name> [environment] [namespace] [use-simplified]

set -e

# Default values
ENVIRONMENT=${2:-"kalpha"}
NAMESPACE=${3:-"stage-backend"}
USE_SIMPLIFIED=${4:-"true"}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if service name is provided
if [ -z "$1" ]; then
    print_error "Service name is required"
    echo "Usage: $0 <service-name> [environment] [namespace] [use-simplified]"
    echo ""
    echo "Available services:"
    echo "  - n-authentication"
    echo "  - n-data-integrations"
    echo "  - n-live-data-streaming"
    echo "  - n-machine-maintenance"
    echo ""
    echo "Examples:"
    echo "  $0 n-authentication"
    echo "  $0 n-authentication kalpha stage-backend true"
    echo "  $0 n-machine-maintenance kalpha stage-backend false  # Use legacy values"
    exit 1
fi

SERVICE_NAME=$1
RELEASE_NAME="${ENVIRONMENT}-${SERVICE_NAME}"
CHART_PATH="./helmCharts/backend/backend-service-chart"

# Check if chart directory exists
if [ ! -d "$CHART_PATH" ]; then
    print_error "Chart directory not found: $CHART_PATH"
    exit 1
fi

# Determine values file to use
if [ "$USE_SIMPLIFIED" = "true" ]; then
    SERVICE_VALUES_FILE="$CHART_PATH/values/$ENVIRONMENT/$SERVICE_NAME/values-simplified.yaml"
    COMMON_VALUES_FILE="$CHART_PATH/values.yaml"
    
    if [ ! -f "$SERVICE_VALUES_FILE" ]; then
        print_warning "Simplified values file not found: $SERVICE_VALUES_FILE"
        print_info "Falling back to legacy values file"
        USE_SIMPLIFIED="false"
    fi
fi

if [ "$USE_SIMPLIFIED" = "false" ]; then
    SERVICE_VALUES_FILE="$CHART_PATH/values/$ENVIRONMENT/$SERVICE_NAME/values.yaml"
    COMMON_VALUES_FILE=""
fi

# Check if service values file exists
if [ ! -f "$SERVICE_VALUES_FILE" ]; then
    print_error "Service values file not found: $SERVICE_VALUES_FILE"
    exit 1
fi

# Build helm command
HELM_CMD="helm upgrade --install $RELEASE_NAME $CHART_PATH"

if [ "$USE_SIMPLIFIED" = "true" ] && [ -f "$COMMON_VALUES_FILE" ]; then
    HELM_CMD="$HELM_CMD -f $COMMON_VALUES_FILE"
    print_info "Using common values: $COMMON_VALUES_FILE"
fi

HELM_CMD="$HELM_CMD -f $SERVICE_VALUES_FILE"
HELM_CMD="$HELM_CMD --namespace $NAMESPACE"
HELM_CMD="$HELM_CMD --create-namespace"

print_info "Deploying service: $SERVICE_NAME"
print_info "Environment: $ENVIRONMENT"
print_info "Namespace: $NAMESPACE"
print_info "Release name: $RELEASE_NAME"
print_info "Using simplified values: $USE_SIMPLIFIED"
print_info "Service values: $SERVICE_VALUES_FILE"

# Show the command that will be executed
print_info "Executing: $HELM_CMD"

# Ask for confirmation
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_info "Deployment cancelled"
    exit 0
fi

# Execute the helm command
print_info "Starting deployment..."
if eval $HELM_CMD; then
    print_info "Deployment completed successfully!"
    
    # Show deployment status
    print_info "Checking deployment status..."
    kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=backend-service-chart,app.kubernetes.io/instance=$RELEASE_NAME
    
    print_info "Service endpoints:"
    kubectl get svc -n $NAMESPACE -l app.kubernetes.io/name=backend-service-chart,app.kubernetes.io/instance=$RELEASE_NAME
    
else
    print_error "Deployment failed!"
    exit 1
fi
