# Values Structure Documentation

## Overview

The Helm chart values have been restructured to follow a common values pattern with service-specific overrides. This approach reduces duplication and makes it easier to maintain consistent configurations across all services.

## File Structure

```
helmCharts/backend/backend-service-chart/
├── values.yaml                           # Common default values for all services
└── values/kalpha/
    ├── n-authentication/
    │   ├── values.yaml                    # Original service-specific values (legacy)
    │   └── values-simplified.yaml         # New simplified service-specific overrides
    ├── n-data-integrations/
    │   ├── values.yaml                    # Original service-specific values (legacy)
    │   └── values-simplified.yaml         # New simplified service-specific overrides
    ├── n-live-data-streaming/
    │   ├── values.yaml                    # Original service-specific values (legacy)
    │   └── values-simplified.yaml         # New simplified service-specific overrides
    └── n-machine-maintenance/
        ├── values.yaml                    # Original service-specific values (legacy)
        └── values-simplified.yaml         # New simplified service-specific overrides
```

## Common Values (values.yaml)

The root `values.yaml` file contains all common default values that are shared across services:

### Key Sections:
- **Basic Configuration**: environment, envType, hostnameSuffix, etc.
- **Database Defaults**: postgres, mongo, kafka configurations with sensible defaults
- **Service Configuration**: default service ports, probe settings, etc.
- **Application Settings**: default JVM settings, app configuration, etc.
- **Infrastructure**: default ingress, autoscaling, security contexts, etc.
- **Heapdump Configuration**: EFS volume settings for heap dumps

### Default Values Include:
- `environment: "kalpha"`
- `envType: "stage"`
- `hostnameSuffix: ".strawmine.com"`
- `replicaCount: 1`
- `scriptDebuggingEnabled: false`
- Default probe configurations
- Default JVM memory settings (256m/256m)
- Default service ports (9000, 9050)
- EFS heapdump volume configuration

## Service-Specific Values

Each service has a simplified values file (`values-simplified.yaml`) that contains only the service-specific overrides:

### Required Overrides:
- `serviceName`: The name of the service
- `image.repository`: Service-specific Docker image repository
- `image.tag`: Service-specific image tag

### Common Service-Specific Overrides:
- **Database Configurations**: Service-specific database clusters, connection strings
- **Ingress Settings**: Service-specific hosts, paths, annotations
- **Resource Requirements**: Service-specific memory/CPU settings
- **Replica Counts**: Service-specific scaling requirements
- **Feature Flags**: Service-specific enabled/disabled features
- **Endpoint Overrides**: Service-specific external service URLs
- **Java Agents**: Service-specific monitoring and tracing configurations

## Usage

### Deploying a Service

To deploy a service using the new structure:

```bash
# Using the simplified values (recommended)
helm upgrade --install kalpha-n-authentication ./helmCharts/backend/backend-service-chart \
  -f ./helmCharts/backend/backend-service-chart/values.yaml \
  -f ./helmCharts/backend/backend-service-chart/values/kalpha/n-authentication/values-simplified.yaml \
  --namespace stage-backend

# Using legacy values (for backward compatibility)
helm upgrade --install kalpha-n-authentication ./helmCharts/backend/backend-service-chart \
  -f ./helmCharts/backend/backend-service-chart/values/kalpha/n-authentication/values.yaml \
  --namespace stage-backend
```

### Value Precedence

When using multiple values files, Helm applies them in order with later files overriding earlier ones:

1. Chart default values (if any)
2. Common values (`values.yaml`)
3. Service-specific values (`values-simplified.yaml`)

## Migration Guide

### For New Services:
1. Create a new service-specific values file based on the simplified template
2. Override only the values that differ from the common defaults
3. Use the common values file as the base

### For Existing Services:
1. The original `values.yaml` files are preserved for backward compatibility
2. New simplified `values-simplified.yaml` files have been created
3. Gradually migrate to use the simplified approach

## Benefits

1. **Reduced Duplication**: Common values are defined once in the root values.yaml
2. **Easier Maintenance**: Changes to common settings only need to be made in one place
3. **Consistency**: All services inherit the same base configuration
4. **Clarity**: Service-specific files only contain what's unique to that service
5. **Flexibility**: Services can still override any common value when needed

## Service-Specific Configurations

### n-authentication
- Enables analytic Kafka
- Has specific host aliases for unified-id
- Uses auth_services ingress context
- Enables external client endpoints

### n-data-integrations
- Disables analytic Kafka
- Has specific host aliases for unified-id
- Uses core_platform_services ingress context
- Debugging enabled

### n-live-data-streaming
- Enables analytic Kafka
- Has specific service endpoint overrides
- Uses streaming_services ingress context
- Has specific S3 bucket configuration

### n-machine-maintenance
- Higher replica count (3)
- Larger memory allocation (350m/350m)
- Enables S3 integration
- Uses maintenance_services ingress context

## Best Practices

1. **Keep service-specific files minimal**: Only override what's necessary
2. **Use descriptive comments**: Explain why specific overrides are needed
3. **Group related configurations**: Keep database, ingress, etc. settings together
4. **Validate configurations**: Test deployments with both common and service-specific values
5. **Document service-specific requirements**: Explain unique configurations in comments
