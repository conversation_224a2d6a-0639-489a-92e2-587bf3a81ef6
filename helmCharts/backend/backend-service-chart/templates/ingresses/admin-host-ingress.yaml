# Start of ingess.yaml
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}

{{- range $admin_endpoint := .Values.ingress.admin_endpoint }}
# Start of range for {{$admin_endpoint.host}}

{{- range $block := $.Values.ingress.blocks }}
{{- $location := "" }}
{{- if eq $block.location "/" }}
  {{- $location = "" }}
{{- else }}
  {{- $location = $block.location }}
{{- end }}
# Ingress for {{ $admin_endpoint.host }} for block {{ $block.name }} for location "{{ $location }}"
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $serviceName }}-{{ $admin_endpoint.host }}-admin-ingress-{{ $block.name }}
  labels:
    {{- include "backend-service-chart.labels" $ | nindent 4 }}
  namespace: {{ $.Release.Namespace }}
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    cert-manager.io/cluster-issuer: letsencrypt-issuer
    {{- if and $.Values.ingress.http_context.context $.Values.ingress.http_context.enabled }}
    nginx.ingress.kubernetes.io/rewrite-target: "/{{ $.Values.ingress.http_context.context }}{{ $location }}/$1"
    {{- else }}
    nginx.ingress.kubernetes.io/rewrite-target: "{{ $location }}/$1"
    {{- end }}
    {{- with $block.additionalAnnotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with $admin_endpoint.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  ingressClassName: {{ $.Values.ingress.className | default "external-nginx" }}
  rules:
    - host: {{ $admin_endpoint.host }}
      http:
        paths:
        {{- if $.Values.ingress.http_context.context }}
          # Path with http_context
          - path: /{{ $.Values.ingress.http_context.context }}{{ $location }}/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ $qualifiedServiceName }}
                port:
                  number: 80
        {{- end }}
          # Path without http_context                  
          - path: {{ $location }}/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ $qualifiedServiceName }}
                port:
                  number: 80
  tls:
  - hosts:
    - {{ $admin_endpoint.host }}
    secretName: {{ $admin_endpoint.host }}-tls
---
{{- end }}
---
{{- end }}