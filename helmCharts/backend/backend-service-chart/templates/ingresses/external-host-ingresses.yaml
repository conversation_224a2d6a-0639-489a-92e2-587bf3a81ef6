# Start of ingess.yaml
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}

{{- range $clientEndpoint := .Values.ingress.client_endpoint }}
  # Start of range for {{$clientEndpoint.host}}
  {{- $ext := "" }}
  {{- if $clientEndpoint.ext_enabled }}
    {{- $ext = "/ext" }}
  {{- else }}
    {{- $ext = "" }}
  {{- end }}

  {{- $serviceIdentifier := "" }}
  {{- if eq $clientEndpoint.serviceIdentifier "" }}
    {{- $serviceIdentifier = "" }}
  {{- else }}
    {{- $serviceIdentifier = printf "/%s" $clientEndpoint.serviceIdentifier }}
  {{- end }}


  {{- range $block := $.Values.ingress.blocks }}
    {{- $location := "" }}
    {{- if eq $block.location "/" }}
      {{- $location = "" }}
    {{- else }}
      {{- $location = $block.location }}
    {{- end }}
# Ingress for {{ $clientEndpoint.host }} for block {{ $block.name }} for location "{{ $location }}" and ext as "{{ $ext }}"
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $serviceName }}-{{ $clientEndpoint.host }}-{{ $clientEndpoint.name }}-external-ingress-{{ $block.name }}
  labels:
    {{- include "backend-service-chart.labels" $ | nindent 4 }}
  namespace: {{ $.Release.Namespace }}
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer
    nginx.ingress.kubernetes.io/use-regex: "true"

    {{- if and $.Values.ingress.http_context.context $.Values.ingress.http_context.enabled }}
    nginx.ingress.kubernetes.io/rewrite-target: "/{{ $.Values.ingress.http_context.context }}{{ $ext }}{{ $location }}/$1"
    {{- else }}
    nginx.ingress.kubernetes.io/rewrite-target: "{{ $ext }}{{ $location }}/$1"
    {{- end }}
    {{- with $block.additionalAnnotations }}
    {{- toYaml . | nindent 4 }} # This converts the map to YAML and indents it correctly
    {{- end }}
    {{- with $clientEndpoint.annotations }}
    {{- toYaml . | nindent 4 }} # This converts the map to YAML and indents it correctly
    {{- end }}
spec:
  ingressClassName: {{ $.Values.ingress.className | default "external-nginx" }}
  rules:
    - host: {{ $clientEndpoint.host }}
      http:
        paths:
        {{- if $.Values.ingress.http_context.context }}
          # Path with http_context
          - path: {{ $serviceIdentifier }}/{{ $.Values.ingress.http_context.context }}{{ $ext }}{{ $location }}/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ $qualifiedServiceName }}
                port:
                  number: 80
        {{- end }}
          # Path without http_context                  
          - path: {{ $serviceIdentifier }}{{ $ext }}{{ $location }}/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ $qualifiedServiceName }}
                port:
                  number: 80
  tls:
  - hosts:
    - {{ $clientEndpoint.host }}
    secretName: {{ $clientEndpoint.host }}-tls
---
  {{- end }}
---
{{- end }}