{{- if .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: {{ $qualifiedServiceName }}-heapdump-pv
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
spec:
  capacity:
    storage: {{ .Values.heapdump.storage }}
  volumeMode: Filesystem
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Retain
  storageClassName: {{ .Values.heapdump.storageClassName }}
  csi:
    driver: efs.csi.aws.com
    volumeHandle: {{ .Values.heapdump.efsFileSystemId }}
    volumeAttributes:
      path: {{ .Values.heapdump.efsPath | default "/" }}
{{- end }}
