{{- if .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ $qualifiedServiceName }}-heapdump-pvc
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
spec:
  accessModes:
    - ReadWriteMany
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{ .Values.heapdump.storage }}
  storageClassName: {{ .Values.heapdump.storageClassName }}
  volumeName: {{ $qualifiedServiceName }}-heapdump-pv
{{- end }}
