{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: v1
kind: Service
metadata:
  name: {{ $qualifiedServiceName }}-headless
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
spec:
  clusterIP: None
  ports:
    {{- range .Values.service.ports }}
    - name: {{ .name }}
      port: {{ .port }}
      targetPort: {{ .targetPort }}
      protocol: TCP
    {{- end }}
  selector:
    {{- include "backend-service-chart.selectorLabels" . | nindent 4 }}
