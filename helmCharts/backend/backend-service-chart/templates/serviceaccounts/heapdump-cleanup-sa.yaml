{{- if .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "backend-service-chart.serviceAccountName" . }}-heapdump-cleanup
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
    component: heapdump-cleanup
  annotations:
    {{- if .Values.heapdump.serviceAccount.roleArn }}
    eks.amazonaws.com/role-arn: {{ .Values.heapdump.serviceAccount.roleArn }}
    {{- end }}
    {{- with .Values.heapdump.serviceAccount.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
automountServiceAccountToken: true
{{- end }}
