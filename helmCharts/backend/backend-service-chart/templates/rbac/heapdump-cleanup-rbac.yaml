{{- if .Values.heapdump.enabled }}
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ $qualifiedServiceName }}-heapdump-cleanup
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
    component: heapdump-cleanup
rules:
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $qualifiedServiceName }}-heapdump-cleanup
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
    component: heapdump-cleanup
subjects:
- kind: ServiceAccount
  name: {{ include "backend-service-chart.serviceAccountName" . }}-heapdump-cleanup
  namespace: {{ .Release.Namespace }}
roleRef:
  kind: Role
  name: {{ $qualifiedServiceName }}-heapdump-cleanup
  apiGroup: rbac.authorization.k8s.io
{{- end }}
