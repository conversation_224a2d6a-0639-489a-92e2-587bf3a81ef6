{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
{{- $headlessServiceName := printf "%s-%s-headless" $environment $serviceName }}
{{- $namespace := .Release.Namespace -}}
{{- $svcTargetPort := .Values.service.targetPort -}}
{{- $replicaCount := .Values.replicaCount | int -}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $qualifiedServiceName }}-cluster-context
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
data:
  {{- range $i, $e := until $replicaCount }}
  service_cluster_context_v2-pod-{{ $i }}: |
    service_cluster_context_v2 {
      consul.catalog_url = "http://prod-consul.zl.internal/v1/catalog"
      file.path : "/tmp/.nsg1-n-event-hub-cluster-other-node-context-v2"
      instances {
        others : [
          {{- range $j, $e := until $replicaCount }}
          {{- if ne $i $j }}
          { 
            name: "{{ $qualifiedServiceName }}-{{ $j }}", 
            ip : "{{ $qualifiedServiceName }}-{{ $j }}.{{ $headlessServiceName }}.{{ $namespace }}.svc.cluster.local:{{ $svcTargetPort }}", 
            serviceInstanceRole : "{{ if eq $j 0 }}master{{ else }}slave{{ end }}" 
          },
          {{- end }}
          {{- end }}
        ]
      }
    }
  {{- end }}
