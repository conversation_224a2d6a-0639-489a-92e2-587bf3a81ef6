{{- if .Values.postgres.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.environment }}-{{ .Values.serviceName }}-postgres-configmap
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}
data:
  postgres-override.conf: |
    {{- $envType := .Values.envType }}
    {{- $env := .Values.environment }}
    {{- $serviceName := .Values.serviceName }}
    {{- $username := default (printf "%s-%s" $env $serviceName | replace "-" "_")  .Values.postgres.username }}
    {{- $dbName := default $username .Values.postgres.dbName }}
    {{- $cluster := .Values.postgres.cluster }}
    {{- $port := default 5432 .Values.postgres.port }}
    {{ $envType }}.{{ $env }}.db.default.user = "{{ $username }}"
    {{ $envType }}.{{ $env }}.db.default.properties.user = "{{ $username }}"
    {{ $envType }}.{{ $env }}.db.driver = "org.postgresql.Driver"
    {{ $envType }}.{{ $env }}.db.default.url = "jdbc:postgresql://{{ $cluster }}:{{$port}}/{{ $dbName }}?sslmode=disable"
    {{ $envType }}.{{ $env }}.db.default.properties.url = "jdbc:postgresql://{{ $cluster }}:{{$port}}/{{ $dbName }}?sslmode=disable"
    {{ $envType }}.{{ $env }}.db.dataSourceClass = "org.postgresql.ds.PGSimpleDataSource"
    {{- range .Values.postgres.dbConnectionpools }}
    {{ $envType }}.{{ $env }}.db.{{ .name }}.maxConnections = "{{ .maxConnections }}"
    {{ $envType }}.{{ $env }}.db.{{ .name }}.minConnections = "{{ .minConnections }}"
    {{ $envType }}.{{ $env }}.db.{{ .name }}.connectionTimeout = "{{ .connectionTimeout }}"
    {{ $envType }}.{{ $env }}.db.{{ .name }}.numThreads = "{{ .numThreads }}"
    {{ $envType }}.{{ $env }}.db.{{ .name }}.registerMbeans = "{{ .registerMbeans }}"
    {{- end }}
{{- end }}
