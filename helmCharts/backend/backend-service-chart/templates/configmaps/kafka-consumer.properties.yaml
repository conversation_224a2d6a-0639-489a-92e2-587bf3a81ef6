{{- $env := .Values.environment }}
{{- $serviceName := .Values.serviceName }}

{{- $zkport := int .Values.kafka.zookeeperPort }}
{{- $kafkaPort := int .Values.kafka.port }}
{{- $zookeeperServerString := "" }}
{{- $bootstrapServerString := "" }}
{{- range $index, $element := .Values.kafka.brokerEndpoints }}
  {{- if $index }}
    {{- $bootstrapServerString = printf "%s,%s:%d" $bootstrapServerString $element $kafkaPort }}
    {{- $zookeeperServerString = printf "%s,%s:%d" $zookeeperServerString $element $zkport }}
  {{- else }}
    {{- $bootstrapServerString = printf "%s:%d" $element $kafkaPort }}
    {{- $zookeeperServerString = printf "%s:%d" $element $zkport }}
  {{- end }}
{{- end }}
{{- $maxPollIntervalMs := default "864000000" .Values.kafka.consumer.maxPollIntervalMs }}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $env }}-{{ $serviceName }}-kafka-consumer.properties
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}

data:
  kafka-consumer.properties : |
    ############################# Consumer Basics #############################
    # format: host1:port1,host2:port2 ...
    zkConnect={{ $zookeeperServerString }}
    bootstrap.servers={{ $bootstrapServerString }}
    max.poll.interval.ms = {{ $maxPollIntervalMs }}
    #fetch.message.max.bytes is being read from override.conf