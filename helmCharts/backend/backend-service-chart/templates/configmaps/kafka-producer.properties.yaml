{{- $env := .Values.environment }}
{{- $serviceName := .Values.serviceName }}

{{- $port := int .Values.kafka.port }}
{{- $bootstrapServerString := "" }}
{{- range $index, $element := .Values.kafka.brokerEndpoints }}
  {{- if $index }}
    {{- $bootstrapServerString = printf "%s,%s:%d" $bootstrapServerString $element $port }}
  {{- else }}
    {{- $bootstrapServerString = printf "%s:%d" $element $port }}
  {{- end }}
{{- end }}
{{- $acks := default "-1" .Values.kafka.acks }}
{{- $producerType := default "sync" .Values.kafka.producerType }}
{{- $messageMaxBytes := default "10485760" .Values.kafka.producer.messageMaxBytes }}
{{- $maxRequestSize := default "10485760" .Values.kafka.producer.maxRequestSize }}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $env }}-{{ $serviceName }}-kafka-producer.properties
  labels:
    {{- include "backend-service-chart.labels" . | nindent 4 }}  
data:
  kafka-producer.properties: |
    ############################# Producer Basics #############################

    # list of brokers used for bootstrapping knowledge about the rest of the cluster
    # format: host1:port1,host2:port2 ...
    bootstrap.servers={{ $bootstrapServerString }}

    #Adding Acknowledgement from all , to ensure reliability
    acks={{ $acks }}

    # specifies whether the messages are sent asynchronously (async) or synchronously (sync)
    producer.type={{ $producerType }}

    # specify the compression codec for all data generated: none, gzip, snappy, lz4.
    # the old config values work as well: 0, 1, 2, 3 for none, gzip, snappy, lz4, respectively
    compression.codec=none

    # message encoder
    serializer.class=kafka.serializer.DefaultEncoder

    # Key Serializer
    key.serializer=org.apache.kafka.common.serialization.StringSerializer

    # Value Serializer
    value.serializer=org.apache.kafka.common.serialization.StringSerializer

    ##In order to support larger messages, upto 10MB in size
    message.max.bytes={{ $messageMaxBytes }}
    max.request.size={{ $maxRequestSize }}