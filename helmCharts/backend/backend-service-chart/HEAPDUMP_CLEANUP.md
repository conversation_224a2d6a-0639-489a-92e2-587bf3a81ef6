# Heapdump Cleanup Job Documentation

## Overview

The heapdump cleanup job is a Kubernetes CronJob that runs every 15 minutes to automatically upload Java heap dump files from the EFS volume to S3 and clean up the local files. This ensures that heap dumps don't accumulate on the EFS volume and are safely stored in S3 for later analysis.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Java Pods     │    │  Heapdump       │    │      S3         │
│                 │    │  Cleanup Job    │    │                 │
│ ┌─────────────┐ │    │                 │    │ stage-eks-      │
│ │ Heap Dumps  │ │───▶│ Every 15 mins   │───▶│ heapdumps/      │
│ │ /opt/       │ │    │                 │    │ <pod-name>/     │
│ │ heapdumps/  │ │    │ - Find .hprof   │    │ *.hprof         │
│ │ <pod-name>/ │ │    │ - Upload to S3  │    │                 │
│ └─────────────┘ │    │ - Delete local  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Components

### 1. <PERSON><PERSON><PERSON><PERSON> (`heapdump-cleanup-cronjob.yaml`)
- **Schedule**: `*/15 * * * *` (every 15 minutes)
- **Concurrency**: `Forbid` (prevents overlapping jobs)
- **Image**: `amazon/aws-cli:2.13.25`
- **Volume**: Mounts the same EFS volume as the application pods (read-only)

### 2. ServiceAccount (`heapdump-cleanup-sa.yaml`)
- **IRSA**: Configured with IAM role for S3 access
- **Annotations**: Supports `eks.amazonaws.com/role-arn` for IAM role binding

### 3. RBAC (`heapdump-cleanup-rbac.yaml`)
- **Role**: Permissions to access PVCs and pods
- **RoleBinding**: Binds the role to the service account

### 4. IAM Policy (`heapdump-s3-iam-policy.json`)
- **S3 Permissions**: PutObject, GetObject, ListBucket
- **Bucket**: `stage-k8s-heapdumps`

## Configuration

### Values Configuration

```yaml
heapdump:
  enabled: true
  efsFileSystemId: "fs-0f5d0ec509c876861"
  s3Bucket: "stage-k8s-heapdumps"
  s3Region: "ap-southeast-1"
  serviceAccount:
    roleArn: "arn:aws:iam::ACCOUNT_ID:role/EKS-HeapdumpCleanup-Role"
    annotations: {}
```

### Environment Variables

The cleanup job uses the following environment variables:
- `AWS_DEFAULT_REGION`: S3 region (from values)
- `S3_BUCKET`: Target S3 bucket (from values)
- `SERVICE_NAME`: Qualified service name for logging

## Setup Instructions

### 1. Run the Setup Script

```bash
./helmCharts/backend/backend-service-chart/setup-heapdump-s3.sh
```

This script will:
- Create the S3 bucket `stage-k8s-heapdumps`
- Enable versioning on the bucket
- Set lifecycle policy (delete old versions after 30 days)
- Create IAM policy for S3 access
- Create IAM role with OIDC trust relationship
- Attach the policy to the role

### 2. Update Values Files

Add the IAM role ARN to your service values files:

```yaml
heapdump:
  enabled: true
  efsFileSystemId: "fs-0f5d0ec509c876861"
  s3Bucket: "stage-k8s-heapdumps"
  serviceAccount:
    roleArn: "arn:aws:iam::YOUR_ACCOUNT_ID:role/EKS-HeapdumpCleanup-Role"
```

### 3. Deploy Services

Deploy your services with the updated values:

```bash
helm upgrade --install kalpha-n-authentication ./helmCharts/backend/backend-service-chart \
  -f ./helmCharts/backend/backend-service-chart/values.yaml \
  -f ./helmCharts/backend/backend-service-chart/values/kalpha/n-authentication/values-simplified.yaml \
  --namespace stage-backend
```

## Job Behavior

### File Detection
The job searches for heap dump files in the following patterns:
- `*.hprof` files
- `java_pid*.hprof` files

### Upload Process
1. **Find Files**: Scans `/opt/heapdumps/<pod-name>/` directories
2. **Upload**: Copies files to `s3://stage-k8s-heapdumps/<pod-name>/`
3. **Verify**: Checks if file exists in S3 after upload
4. **Cleanup**: Removes local file only after successful verification

### S3 Structure
```
s3://stage-k8s-heapdumps/
├── kalpha-n-authentication-0/
│   ├── java_pid123.hprof
│   └── heap_dump_2024-01-15.hprof
├── kalpha-n-authentication-1/
│   └── java_pid456.hprof
├── kalpha-n-machine-maintenance-0/
│   └── java_pid789.hprof
└── kalpha-n-data-integrations-0/
    └── heap_dump_2024-01-15.hprof
```

## Monitoring and Troubleshooting

### Check CronJob Status
```bash
kubectl get cronjobs -n stage-backend
kubectl describe cronjob kalpha-n-authentication-heapdump-cleanup -n stage-backend
```

### View Job Logs
```bash
# List recent jobs
kubectl get jobs -n stage-backend | grep heapdump-cleanup

# View logs for a specific job
kubectl logs job/kalpha-n-authentication-heapdump-cleanup-28434560 -n stage-backend
```

### Check S3 Uploads
```bash
aws s3 ls s3://stage-k8s-heapdumps/ --recursive
```

### Common Issues

1. **Permission Denied**: Check IAM role and IRSA configuration
2. **S3 Upload Failed**: Verify S3 bucket exists and permissions are correct
3. **No Files Found**: Check if heap dumps are being generated in the correct location
4. **Job Not Running**: Check CronJob schedule and pod status

### Manual Job Execution
```bash
# Create a manual job from the CronJob
kubectl create job --from=cronjob/kalpha-n-authentication-heapdump-cleanup manual-cleanup -n stage-backend
```

## Security Considerations

1. **Least Privilege**: IAM role only has access to the specific S3 bucket
2. **IRSA**: Uses IAM Roles for Service Accounts for secure credential management
3. **Read-Only Mount**: EFS volume is mounted read-only in the cleanup job
4. **Verification**: Files are only deleted after successful S3 upload verification

## Cost Optimization

1. **Lifecycle Policy**: Old versions are automatically deleted after 30 days
2. **Intelligent Tiering**: Consider enabling S3 Intelligent Tiering for cost optimization
3. **Compression**: Heap dump files are typically large; consider enabling S3 compression

## Maintenance

### Updating the Cleanup Job
- Modify the CronJob template and redeploy the Helm chart
- The job will pick up changes on the next scheduled run

### Changing S3 Bucket
1. Update the `s3Bucket` value in your values files
2. Update the IAM policy to include the new bucket
3. Redeploy the services

### Adjusting Schedule
- Modify the `schedule` field in the CronJob template
- Consider the frequency based on heap dump generation rate and storage costs
