NAME: n-machine-maintenance-web
LAST DEPLOYED: Fri Feb  7 17:19:55 2025
NAMESPACE: stage-backend
STATUS: deployed
REVISION: 1
TEST SUITE: None
USER-SUPPLIED VALUES:
envType: stage
environment: kalpha
hostnameSuffix: .strawmine.com
image:
  pullPolicy: IfNotPresent
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/kalpha-machine-maintenance-web
  tag: v4
ingress:
  blocks:
  - additionalAnnotations: {}
    location: /
    name: root
  className: external-nginx
  client_endpoint:
  - annotations: {}
    host: kalpha-n-machine-maintenance-external.strawmine.com
    name: machine-maintenance-web
    serviceIdentifier: ""
livenessProbe:
  enabled: false
  failureThreshold: 3
  initialDelaySeconds: 30
  path: /admin/api/v1/status/report?consulTest=true
  periodSeconds: 30
  port: http
  successThreshold: 1
  timeoutSeconds: 5
namespace: kalpha-stage-backend
readinessProbe:
  enabled: false
  failureThreshold: 3
  initialDelaySeconds: 10
  path: /admin/api/v1/status/report?consulTest=true
  periodSeconds: 15
  port: http
  successThreshold: 1
  timeoutSeconds: 5
replicaCount: 1
resources: {}
scriptDebuggingEnabled: true
service:
  port: 80
  targetPort: 80
  type: ClusterIP
serviceAccount:
  annotations: {}
  automount: true
  create: true
  name: ""
serviceName: n-machine-maintenance-web
volumeMounts: []

COMPUTED VALUES:
envType: stage
environment: kalpha
hostnameSuffix: .strawmine.com
image:
  pullPolicy: IfNotPresent
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/kalpha-machine-maintenance-web
  tag: v4
ingress:
  blocks:
  - additionalAnnotations: {}
    location: /
    name: root
  className: external-nginx
  client_endpoint:
  - annotations: {}
    host: kalpha-n-machine-maintenance-external.strawmine.com
    name: machine-maintenance-web
    serviceIdentifier: ""
livenessProbe:
  enabled: false
  failureThreshold: 3
  initialDelaySeconds: 30
  path: /admin/api/v1/status/report?consulTest=true
  periodSeconds: 30
  port: http
  successThreshold: 1
  timeoutSeconds: 5
namespace: kalpha-stage-backend
readinessProbe:
  enabled: false
  failureThreshold: 3
  initialDelaySeconds: 10
  path: /admin/api/v1/status/report?consulTest=true
  periodSeconds: 15
  port: http
  successThreshold: 1
  timeoutSeconds: 5
replicaCount: 1
resources: {}
scriptDebuggingEnabled: true
service:
  port: 80
  targetPort: 80
  type: ClusterIP
serviceAccount:
  annotations: {}
  automount: true
  create: true
  name: ""
serviceName: n-machine-maintenance-web
volumeMounts: []

HOOKS:
MANIFEST:
---
# Source: frontend-service-chart/templates/serviceaccounts/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: n-machine-maintenance-web-frontend-service-chart
  labels:
    helm.sh/chart: frontend-service-chart-0.1.0
    app.kubernetes.io/name: frontend-service-chart
    app.kubernetes.io/instance: n-machine-maintenance-web
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
automountServiceAccountToken: true
---
# Source: frontend-service-chart/templates/configmaps/nginx.conf.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-service-chart-nginx-conf-configmap  # Use chart name instead of release name
  annotations:
    "helm.sh/resource-policy": keep  # Prevents Helm from deleting the ConfigMap on chart uninstall
data:
  nginx.conf: |
    user  nginx;
    worker_processes  auto;
    
    error_log  /var/log/nginx/error.log notice;
    pid        /var/run/nginx.pid;
    
    events {
        worker_connections  1024;
    }
    
    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
    
        log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                            '$status $body_bytes_sent "$http_referer" '
                            '"$http_user_agent" "$http_x_forwarded_for"';
    
        access_log  /var/log/nginx/access.log  main;
    
        sendfile        on;
        keepalive_timeout  65;
    
        # Define maps for connection upgrades and keep-alive
        map $http_upgrade $connection_upgrade {
            default          upgrade;
            ''               close;
        }
    
        map $http_upgrade $keepalive_connection_upgrade {
            default          upgrade;
            ''               '';
        }
    
        include /etc/nginx/conf.d/*.conf;
    }
---
# Source: frontend-service-chart/templates/configmaps/nginxConfs.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-service-chart-nginx-configs-configmap  # Use chart name instead of release name
  annotations:
    "helm.sh/resource-policy": keep  # Prevents Helm from deleting the ConfigMap on chart uninstall
data:
  n-machine-maintenance-web.conf: |
    server {
        listen 80 default_server;  # Listen on port 80 and set this block as the default
        # Set headers to pass through relevant request details to the proxied server
        proxy_set_header X-Host $http_host;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-Host $http_x_forwarded_host;
        proxy_set_header X-Real-IP $remote_addr;  # Use $remote_addr to pass the correct client IP
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # Ensure HTTP/1.1 is used for chunked responses
        proxy_http_version 1.1;
    
        # For WebSockets or other upgrade scenarios
        proxy_set_header Upgrade $http_upgrade;
        
        # This may need to be adjusted to ensure keepalive connections work properly:
        proxy_set_header Connection $http_connection;
    
        # Serve the static frontend content
        location / {
            try_files $uri $uri/ /index.html;  # Ensure single-page app fallback to index.html
            root /app/www;  # Ensure /app/www contains your static files
            client_max_body_size 50M;  # Adjust as necessary for file uploads
    
            # Proxy settings
            proxy_send_timeout 300;
            proxy_read_timeout 300;
        }
    }
---
# Source: frontend-service-chart/templates/services/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: kalpha-n-machine-maintenance-web
  labels:
    helm.sh/chart: frontend-service-chart-0.1.0
    app.kubernetes.io/name: frontend-service-chart
    app.kubernetes.io/instance: n-machine-maintenance-web
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: frontend-service-chart
    app.kubernetes.io/instance: n-machine-maintenance-web
---
# Source: frontend-service-chart/templates/statefulsets/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kalpha-n-machine-maintenance-web
  labels:
    helm.sh/chart: frontend-service-chart-0.1.0
    app.kubernetes.io/name: frontend-service-chart
    app.kubernetes.io/instance: n-machine-maintenance-web
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
spec:
  serviceName: kalpha-n-machine-maintenance-web
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: frontend-service-chart
      app.kubernetes.io/instance: n-machine-maintenance-web
  template:
    metadata:
      labels:
        helm.sh/chart: frontend-service-chart-0.1.0
        app.kubernetes.io/name: frontend-service-chart
        app.kubernetes.io/instance: n-machine-maintenance-web
        app.kubernetes.io/version: "1.16.0"
        app.kubernetes.io/managed-by: Helm
    spec:
      serviceAccountName: n-machine-maintenance-web-frontend-service-chart
      securityContext:
        null

      containers:
        - name: frontend-nginx
          securityContext:
            null
          image: "************.dkr.ecr.ap-southeast-1.amazonaws.com/kalpha-machine-maintenance-web:v4"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: QUALIFIED_SERVICE_NAME
              value: "kalpha-n-machine-maintenance-web" 

          volumeMounts:
          - name: frontend-service-chart-nginx-conf-configmap
            mountPath: /etc/nginx/nginx.conf
            subPath: nginx.conf
          - name: frontend-service-chart-nginx-configs-configmap
            mountPath: /etc/nginx/conf.d/n-machine-maintenance-web.conf
            subPath: n-machine-maintenance-web.conf
      volumes:
      - name: frontend-service-chart-nginx-conf-configmap
        configMap:
          name: frontend-service-chart-nginx-conf-configmap
      - name: frontend-service-chart-nginx-configs-configmap
        configMap:
          name: frontend-service-chart-nginx-configs-configmap
---
# Source: frontend-service-chart/templates/ingresses/external-host-ingresses.yaml
# Start of ingess.yaml
# Ingress for kalpha-n-machine-maintenance-external.strawmine.com for block root for location "" 
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: n-machine-maintenance-web-kalpha-n-machine-maintenance-external.strawmine.com-external-ingress
  labels:
    helm.sh/chart: frontend-service-chart-0.1.0
    app.kubernetes.io/name: frontend-service-chart
    app.kubernetes.io/instance: n-machine-maintenance-web
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
  namespace: stage-backend
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer
    nginx.ingress.kubernetes.io/use-regex: "true"

    nginx.ingress.kubernetes.io/rewrite-target: "/$1"
spec:
  ingressClassName: external-nginx
  rules:
    - host: kalpha-n-machine-maintenance-external.strawmine.com
      http:
        paths:
          - path: /(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: kalpha-n-machine-maintenance-web
                port:
                  number: 80
  tls:
  - hosts:
    - kalpha-n-machine-maintenance-external.strawmine.com
    secretName: kalpha-n-machine-maintenance-external.strawmine.com-tls

NOTES:
1. Get the application URL by running these commands:
  export POD_NAME=$(kubectl get pods --namespace stage-backend -l "app.kubernetes.io/name=frontend-service-chart,app.kubernetes.io/instance=n-machine-maintenance-web" -o jsonpath="{.items[0].metadata.name}")
  export CONTAINER_PORT=$(kubectl get pod --namespace stage-backend $POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
  echo "Visit http://127.0.0.1:8080 to use your application"
  kubectl --namespace stage-backend port-forward $POD_NAME 8080:$CONTAINER_PORT
