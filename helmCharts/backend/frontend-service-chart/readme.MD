How to install:

From the root directory of helm chart:

release=n-machine-maintenance-web
ns=stage-backend
values_file=${release}.yaml


To uninstall the release:
echo "helm delete $release -n $ns"
echo "helm delete --purge $release -n $ns"

To install
echo "helm install --debug $release -n $ns -f ${values_file} . | tee outputs/${release}-install.log"

TO upgrade
helm upgrade --wait $release -n $ns -f ${values_file} .