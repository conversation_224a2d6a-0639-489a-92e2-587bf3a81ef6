{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: v1
kind: Service
metadata:
  name: {{ $qualifiedServiceName }}
  labels:
    {{- include "frontend-service-chart.labels" . | nindent 4 }}
spec:
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "frontend-service-chart.selectorLabels" . | nindent 4 }}