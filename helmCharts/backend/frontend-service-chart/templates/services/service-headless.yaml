{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
apiVersion: v1
kind: Service
metadata:
  name: {{ $qualifiedServiceName }}-headless
  labels:
    {{- include "frontend-service-chart.labels" . | nindent 4 }}
spec:
  clusterIP: None
  ports:
    - port: {{ .Values.service.port }} # This is legacy. We need to call targetPort for headless service, as it is used for DNS to get specific pod IP
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "frontend-service-chart.selectorLabels" . | nindent 4 }}