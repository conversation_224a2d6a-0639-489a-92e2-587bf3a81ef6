{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}
{{- $nginxGlobalConfigConfigMap := "nginx-conf-configmap" }}
{{- $nginxServiceConfigConfigMap := "nginx-configs-configmap" }}
{{- $nginxConfigFile:= printf "%s.conf" $serviceName }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ $qualifiedServiceName }}
  labels:
    {{- include "frontend-service-chart.labels" . | nindent 4 }}
spec:
  serviceName: {{ $qualifiedServiceName }}-headless
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "frontend-service-chart.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "frontend-service-chart.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "frontend-service-chart.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}

      containers:
        - name: frontend-nginx
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.targetPort }}
              protocol: TCP
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.path }}
              port: {{ .Values.livenessProbe.port }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.readinessProbe.enabled }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.path }}
              port: {{ .Values.readinessProbe.port }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.resources }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- end }}
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: QUALIFIED_SERVICE_NAME
              value: "{{ $qualifiedServiceName }}" 

          volumeMounts:
          - name: {{ $nginxGlobalConfigConfigMap }}
            mountPath: /etc/nginx/nginx.conf
            subPath: nginx.conf
          - name: {{ $nginxServiceConfigConfigMap }}
            mountPath: /etc/nginx/conf.d/{{ $serviceName }}.conf
            subPath: {{ $serviceName }}.conf
          {{- if .Values.volumeMounts }}
            {{- toYaml .Values.volumeMounts | nindent 12 }}
          {{- end }}
      volumes:
      - name: {{ $nginxGlobalConfigConfigMap }}
        configMap:
          name: {{ $nginxGlobalConfigConfigMap }}
      - name: {{ $nginxServiceConfigConfigMap }}
        configMap:
          name: {{ $nginxServiceConfigConfigMap }}
  {{- if .Values.createPVC }}
  volumeClaimTemplates:
    {{- toYaml .Values.volumeClaim | nindent 8 }}
  {{- end }}
