# Start of ingess.yaml
{{- $environment := .Values.environment }}
{{- $serviceName := .Values.serviceName }}
{{- $qualifiedServiceName := printf "%s-%s" $environment $serviceName }}

{{- range $clientEndpoint := .Values.ingress.client_endpoint }}

{{- $serviceIdentifier := "" }}
{{- if eq $clientEndpoint.serviceIdentifier "" }}
  {{- $serviceIdentifier = "" }}
{{- else }}
  {{- $serviceIdentifier = printf "/%s" $clientEndpoint.serviceIdentifier }}
{{- end }}


{{- range $block := $.Values.ingress.blocks }}
{{- $location := "" }}
{{- if eq $block.location "/" }}
  {{- $location = "" }}
{{- else }}
  {{- $location = $block.location }}
{{- end }}
# Ingress for {{ $clientEndpoint.host }} for block {{ $block.name }} for location "{{ $location }}" 
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $serviceName }}-{{ $clientEndpoint.host }}-external-ingress
  labels:
    {{- include "frontend-service-chart.labels" $ | nindent 4 }}
  namespace: {{ $.Release.Namespace }}
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-issuer
    nginx.ingress.kubernetes.io/use-regex: "true"

    nginx.ingress.kubernetes.io/rewrite-target: "{{ $location }}/$1"
    {{- with $block.additionalAnnotations }}
    {{- toYaml . | nindent 4 }} # This converts the map to YAML and indents it correctly
    {{- end }}
    {{- with $clientEndpoint.annotations }}
    {{- toYaml . | nindent 4 }} # This converts the map to YAML and indents it correctly
    {{- end }}
spec:
  ingressClassName: {{ $.Values.ingress.className | default "external-nginx" }}
  rules:
    - host: {{ $clientEndpoint.host }}
      http:
        paths:
          - path: {{ $serviceIdentifier }}{{ $location }}/(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: {{ $qualifiedServiceName }}
                port:
                  number: 80
  tls:
  - hosts:
    - {{ $clientEndpoint.host }}
    secretName: {{ $clientEndpoint.host }}-tls

{{- end }}
---
{{- end }}