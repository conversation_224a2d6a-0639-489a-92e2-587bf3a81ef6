# For devops: set to true for debugging the init job 
scriptDebuggingEnabled: True


#REQUIRED
# Sets the service name
serviceName: "n-machine-maintenance-web" 


#REQUIRED
# Sets the environment
environment: "kalpha" 

#REQUIRED
# prod/stage # Used for creating the service conf
envType: "stage" 

replicaCount: 1
image:
  # repository: 185889327143.dkr.ecr.ap-southeast-1.amazonaws.com/kalpha-machine-maintenance-web
  # tag: "v4"
  repository: 185889327143.dkr.ecr.ap-southeast-1.amazonaws.com/machine-maintenance-web
  tag: "k-alpha"

  # 185889327143.dkr.ecr.ap-southeast-1.amazonaws.com/machine-maintenance-web:k-alpha
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.

hostnameSuffix: ".strawmine.com" # Default is .strawmine.com


ingress:
  className: "external-nginx"
  blocks: 
    - name: "root"
      location: "/"
      additionalAnnotations: {}
  client_endpoint:
    - name: "machine-maintenance-web" # This is irrelevant but is used for readability
      host: "kalpha-n-machine-maintenance-web-external.strawmine.com"
      serviceIdentifier: "" # without / prefix
      annotations: {}

service:
  type: ClusterIP
  port: 80
  targetPort: 80


livenessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  port: http
  initialDelaySeconds: 30
  periodSeconds: 30
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3

# Readiness probe configuration
readinessProbe:
  enabled: false
  path: /admin/api/v1/status/report?consulTest=true
  port: http
  initialDelaySeconds: 10
  periodSeconds: 15
  timeoutSeconds: 5
  successThreshold: 1
  failureThreshold: 3


# Resources for the frontend nignx pod serving assets
# resources: {}
resources:
  requests:
    cpu: "50m"   # 50 millicores (0.05 vCPU)
    memory: "64Mi"  # 64 MiB of RAM
  limits:
    cpu: "250m"  # 250 millicores (0.25 vCPU)
    memory: "128Mi"  # 128 MiB of RAM



volumeMounts: []
# # - name: foo
# #   mountPath: "/etc/foo"
# #   readOnly: true

# Service account for the statefulset. Not required at the moment
serviceAccount:
  # Specifies whether a service account should be created
  create: false
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""