How to install:

From the root directory of helm chart:

release=commons
ns=stage-backend
values_file=values/${release}-values.yaml


To uninstall the release:
echo "helm delete $release -n $ns"
echo "helm delete --purge $release -n $ns"

To install
echo "helm install --debug $release -n $ns -f ${values_file} . 2>&1 | tee outputs/${release}-install.log"

TO upgrade
helm upgrade --wait $release -n $ns -f ${values_file} .
