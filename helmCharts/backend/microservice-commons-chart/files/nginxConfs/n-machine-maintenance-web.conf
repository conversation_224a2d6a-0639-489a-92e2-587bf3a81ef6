server {
    listen 80 default_server;  # Listen on port 80 and set this block as the default
    # Set headers to pass through relevant request details to the proxied server
    proxy_set_header X-Host $http_host;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-Host $http_x_forwarded_host;
    proxy_set_header X-Real-IP $remote_addr;  # Use $remote_addr to pass the correct client IP
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    
    # Ensure HTTP/1.1 is used for chunked responses
    proxy_http_version 1.1;

    # For WebSockets or other upgrade scenarios
    proxy_set_header Upgrade $http_upgrade;
    
    # This may need to be adjusted to ensure keepalive connections work properly:
    proxy_set_header Connection $http_connection;

    # Serve the static frontend content
    location / {
        try_files $uri $uri/ /index.html;  # Ensure single-page app fallback to index.html
        root /app/www;  # Ensure /app/www contains your static files
        client_max_body_size 50M;  # Adjust as necessary for file uploads

        # Proxy settings
        proxy_send_timeout 300;
        proxy_read_timeout 300;
    }
}