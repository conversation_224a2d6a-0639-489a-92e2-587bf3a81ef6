user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;

    # Define maps for connection upgrades and keep-alive
    map $http_upgrade $connection_upgrade {
        default          upgrade;
        ''               close;
    }

    map $http_upgrade $keepalive_connection_upgrade {
        default          upgrade;
        ''               '';
    }

    include /etc/nginx/conf.d/*.conf;
}