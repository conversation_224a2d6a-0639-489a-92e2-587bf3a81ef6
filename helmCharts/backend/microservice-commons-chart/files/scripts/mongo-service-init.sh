#!/bin/bash

################################################################################
# MongoDB Service Initialization Script
#
# Description:
# This script automates the setup of MongoDB users by managing user accounts
# and assigning roles in a MongoDB database environment. The following
# functionalities are provided:
#
# 1. **Environment and Credentials Setup**:
#    - Retrieves MongoDB admin and service user credentials from Kubernetes Secrets.
#    - Credentials are stored in `/etc/db-admin-secret` and `/etc/mongo-service-secret`.
#    - Sets necessary environment variables to connect to the MongoDB instance 
#      (`MONGO_HOST`, `MONGO_ADMIN_USER`, `MONGO_ADMIN_PASS`, etc.).
#
# 2. **User and Role Management**:
#    - Checks if a MongoDB service user (`MONGO_SERVICE_USER`) already exists 
#      in the specified target database (`MONGO_DB_TO_CONNECT`).
#    - If the user does not exist, it creates the user with the given password 
#      and roles (`MONGO_ROLES_JSON`).
#    - If the user already exists but has different roles, it updates the roles
#      to match the provided roles (`MONGO_ROLES_JSON`).
#
# 3. **User Verification**:
#    - Provides feedback on the operation's success or failure after creating or
#      updating a user.
#
# 4. **Databases for User Creation**:
#    - Development and global access users are created in the admin database.
#    - Service users are created in the `NSP_Users` database.
#
# Usage:
# To execute this script, ensure that all the required environment variables are set.
# - `MONGO_HOST`: The MongoDB host to connect to.
# - `ENVIRONMENT`: The environment context (e.g., "staging", "production") for secrets.
# - `MONGO_AUTHENTICATION_DATABASE`: The authentication database (e.g., "NSP_Users").
# - `MONGO_SERVICE_USER`: The service user that will be managed or created.
# - `MONGO_ROLES_JSON`: The roles to assign to the user (in JSON format).
#
# Example of `MONGO_ROLES_JSON`:
# '[{"db":"orders","role":"readWrite"},{"db":"sales","role":"read"}]'
#
################################################################################

# Enable script debugging if SCRIPT_DEBUGGING_ENABLED is set to true
if [[ $SCRIPT_DEBUGGING_ENABLED == "true" ]]; then
    echo "DEBUGGING ENABLED"
    set -x
fi

# Check if necessary environment variables are set
required_vars=("MONGO_HOST" "ENVIRONMENT" "MONGO_AUTHENTICATION_DATABASE" "MONGO_SERVICE_USER" "MONGO_ROLES_JSON")
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "Error: $var is not set. Please ensure all required environment variables are provided."
        exit 1
    fi
done

# Retrieve MongoDB Admin Credentials
MONGO_ADMIN_USER=$(jq -r ".mongo.\"${MONGO_HOST}\".admin_user" < /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret)
MONGO_ADMIN_PASS=$(jq -r ".mongo.\"${MONGO_HOST}\".admin_password" < /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret)

# Check if the MongoDB Admin Credentials were successfully retrieved
if [[ -z "$MONGO_ADMIN_USER" || -z "$MONGO_ADMIN_PASS" ]]; then
    echo "Error: Failed to retrieve MongoDB admin credentials for host $MONGO_HOST."
    exit 1
fi

# Retrieve MongoDB Service User Credentials
MONGO_SERVICE_PASS=$(cat /etc/mongo-service-secret/password)
if [[ -z "$MONGO_SERVICE_PASS" ]]; then
    echo "Error: MongoDB service user password is not set."
    exit 1
fi

# Function to check if a MongoDB user exists in a given database
# Returns 0 if the user exists, 1 otherwise
mongo_user_exists() {
    local user=$1
    local user_db=$2
    echo "Checking if user '$user' exists in database '$user_db'..."

    mongo --host "$MONGO_HOST"   --authenticationDatabase "admin" -u "$MONGO_ADMIN_USER" -p "$MONGO_ADMIN_PASS" --quiet <<EOF
use ${user_db}
var user = db.getUser('$user');
if (user) {
    quit(0);
} else {
    quit(1);
}
EOF
    return $?
}

# Function to get the roles of a MongoDB user
# Outputs the roles in JSON format
mongo_get_user_roles() {
    local user=$1
    local user_db=$2

    mongo --host "$MONGO_HOST"   --authenticationDatabase "admin" -u "$MONGO_ADMIN_USER" -p "$MONGO_ADMIN_PASS" --quiet <<EOF | grep -E '^\[|^\{'
use ${user_db}
var user = db.getUser('$user');
if (user) {
    print(JSON.stringify(user.roles));
    quit(0);
} else {
    quit(1);
}
EOF
}

# Function to create or update a MongoDB user with specified roles in a given database
mongo_create_or_update_user() {
    local user=$1
    local pass=$2
    local roles=$3
    local user_db=$4

    if mongo_user_exists "$user" "$user_db"; then
        echo "MongoDB user '$user' already exists in database '$user_db'."

        # Get existing roles of the user
        existing_roles=$(mongo_get_user_roles "$user" "$user_db")
        if [[ $? -ne 0 ]]; then
            echo "Error: Failed to retrieve roles for user '$user'."
            exit 1
        fi

        # Normalize JSON strings for comparison using jq
        normalized_existing_roles=$(echo "$existing_roles" | jq -c 'sort_by(.db, .role)' 2>/dev/null)
        normalized_provided_roles=$(echo "$roles" | jq -c 'sort_by(.db, .role)')

        # Check if jq returned an error for existing roles
        if [[ -z "$normalized_existing_roles" ]]; then
            echo "Error: Failed to parse existing roles for user '$user'."
            exit 1
        fi

        # Compare the two JSON role lists using jq
        if echo "$normalized_existing_roles" | jq --argjson roles "$normalized_provided_roles" -e 'if . == $roles then true else false end' >/dev/null; then
            echo "Roles for user '$user' are already up-to-date."
        else
            echo "Updating roles for user '$user' in database '$user_db'..."
            mongo --host "$MONGO_HOST"   --authenticationDatabase "admin" -u "$MONGO_ADMIN_USER" -p "$MONGO_ADMIN_PASS" <<EOF
use ${user_db}
db.updateUser("$user", { roles: $roles })
EOF

            if [[ $? -ne 0 ]]; then
                echo "Error: Failed to update roles for user '$user'."
                exit 1
            else
                echo "Roles for user '$user' updated successfully."
            fi
        fi
    else
        echo "Creating MongoDB user '$user' in database '$user_db' with roles: $roles"

        mongo --host "$MONGO_HOST"   --authenticationDatabase "admin" -u "$MONGO_ADMIN_USER" -p "$MONGO_ADMIN_PASS" <<EOF
use ${user_db}
db.createUser({user: "$user", pwd: "$pass", roles: $roles})
EOF

        if [[ $? -ne 0 ]]; then
            echo "Error: Failed to execute MongoDB command to create user '$user'."
            exit 1
        fi

        # Check if the user creation was successful
        if mongo_user_exists "$user" "$user_db"; then
            echo "MongoDB user '$user' created successfully in database '$user_db'."
        else
            echo "Error: Failed to create MongoDB user '$user' in database '$user_db'."
            exit 1
        fi
    fi
}

# Run the function to create or update the MongoDB service user
mongo_create_or_update_user "$MONGO_SERVICE_USER" "$MONGO_SERVICE_PASS" "$MONGO_ROLES_JSON" "$MONGO_AUTHENTICATION_DATABASE"
