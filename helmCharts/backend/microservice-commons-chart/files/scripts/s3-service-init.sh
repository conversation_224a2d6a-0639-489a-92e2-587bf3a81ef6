#!/bin/bash

################################################################################
# Script: s3-service-init.sh
# Author: <Your Name or Company>
# Purpose:
# Automates the setup of AWS S3 buckets, IAM policies, and Kubernetes secrets
# for services that interact with S3. Ensures that required S3 buckets, IAM users,
# and access policies are in place, and securely stores access keys in Kubernetes secrets.
################################################################################

# Enable debugging if specified
if [[ $SCRIPT_DEBUGGING_ENABLED == "true" ]]; then
    echo "DEBUGGING ENABLED"
    set -x
fi

# Set AWS region with fallback
export AWS_REGION=${S3_REGION:-'ap-southeast-1'}

# Function to create an S3 bucket if it doesn't exist
create_s3_bucket() {
    local bucket_name=$1
    echo "Checking if S3 bucket '$bucket_name' exists..."
    if ! aws s3 ls "s3://$bucket_name" --region "$AWS_REGION" >/dev/null 2>&1; then
        echo "Bucket '$bucket_name' does not exist. Creating..."
        aws s3api create-bucket --bucket "$bucket_name" --region "$AWS_REGION" --create-bucket-configuration LocationConstraint="$AWS_REGION" 2>/dev/null
        if [[ $? -eq 0 ]]; then
            echo "Bucket '$bucket_name' created successfully."
        else
            echo "Error occurred while creating bucket '$bucket_name'."
        fi
    else
        echo "Bucket '$bucket_name' already exists."
    fi
}

# Function to create IAM policy for S3 bucket
create_s3_policy() {
    local bucket_name=$1
    local acl=$2
    local policy_name="${bucket_name}_${acl}"
    echo "Checking if IAM policy '$policy_name' exists..."
    local policy_arn=$(aws iam list-policies --query "Policies[?PolicyName=='$policy_name'].Arn" --output text 2>/dev/null)

    if [[ -z "$policy_arn" || "$policy_arn" == "None" ]]; then
        echo "Policy '$policy_name' does not exist. Creating..."
        local policy_document
        if [[ "$acl" == "read" ]]; then
            policy_document='{
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Action": ["s3:GetObject", "s3:ListBucket"],
                        "Resource": ["arn:aws:s3:::'"$bucket_name"'", "arn:aws:s3:::'"$bucket_name"'/*"]
                    }
                ]
            }'
        elif [[ "$acl" == "write" ]]; then
            policy_document='{
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Action": ["s3:GetObject", "s3:ListBucket", "s3:PutObject", "s3:DeleteObject"],
                        "Resource": ["arn:aws:s3:::'"$bucket_name"'", "arn:aws:s3:::'"$bucket_name"'/*"]
                    }
                ]
            }'
        fi

        aws iam create-policy --policy-name "$policy_name" --policy-document "$policy_document" 2>/dev/null
        if [[ $? -eq 0 ]]; then
            echo "Policy '$policy_name' created successfully."
        else
            echo "Error occurred while creating policy '$policy_name'."
        fi
    else
        echo "Policy '$policy_name' already exists."
    fi
}

# Function to create an IAM user if it doesn't exist
create_iam_user() {
    local iam_user=$1
    echo "Checking if IAM user '$iam_user' exists..."
    aws iam get-user --user-name "$iam_user" --output text >/dev/null 2>&1
    if [[ $? -ne 0 ]]; then
        echo "IAM user '$iam_user' does not exist. Creating..."
        aws iam create-user --user-name "$iam_user"
        if [[ $? -eq 0 ]]; then
            echo "IAM user '$iam_user' created successfully."
        else
            echo "Error occurred while creating IAM user '$iam_user'. Exiting."
            exit 1
        fi
    else
        echo "IAM user '$iam_user' already exists."
    fi
}

# Function to attach a policy to IAM user if not already attached
attach_policy_to_user() {
    local iam_user=$1
    local policy_arn=$2
    echo "Checking if policy '$policy_arn' is attached to user '$iam_user'..."
    local policy_attached=$(aws iam list-attached-user-policies --user-name "$iam_user" --query "AttachedPolicies[?PolicyArn=='$policy_arn'].PolicyArn" --output text 2>/dev/null)

    if [[ -z "$policy_attached" || "$policy_attached" == "None" ]]; then
        echo "Attaching policy '$policy_arn' to user '$iam_user'..."
        aws iam attach-user-policy --user-name "$iam_user" --policy-arn "$policy_arn"
        if [[ $? -eq 0 ]]; then
            echo "Policy '$policy_arn' attached to user '$iam_user' successfully."
        else
            echo "Error occurred while attaching policy '$policy_arn' to user '$iam_user'."
        fi
    else
        echo "Policy '$policy_arn' is already attached to user '$iam_user'."
    fi
    
}

# Function to check if Kubernetes secret exists
secret_exists() {
    local secret_name=$1
    echo "Checking if Kubernetes secret '$secret_name' exists..."
    if kubectl get secret "$secret_name" >/dev/null 2>&1; then
        echo "Kubernetes secret '$secret_name' exists."
        return 0
    else
        echo "Kubernetes secret '$secret_name' does not exist."
        return 1
    fi
}

# Function to create a Kubernetes secret
create_k8s_secret() {
    local secret_name=$1
    local access_key=$2
    local secret_key=$3
    local config_content=$(cat <<EOF
${ENV_TYPE}.${ENVIRONMENT}.aws.s3 {
    access-key-id : "$access_key"
    secret-access-key : "$secret_key"
    region : "$AWS_REGION"
}
EOF
    )
    echo "Creating Kubernetes secret '$secret_name'..."
    kubectl create secret generic "$secret_name" --from-literal=s3-access-override.conf="$config_content"
    if [[ $? -eq 0 ]]; then
        echo "Kubernetes secret '$secret_name' created successfully."
    else
        echo "Error occurred while creating Kubernetes secret '$secret_name'."
    fi
    
}

# MAIN FLOW
echo "Starting S3 Service Initialization..."
iam_user="${ENV_TYPE}-${ENVIRONMENT}-${SERVICE_NAME}-s3-user"
create_iam_user "$iam_user"

secret_name="${ENVIRONMENT}-${SERVICE_NAME}-s3-credentials"
if secret_exists "$secret_name"; then
    echo "Kubernetes secret '$secret_name' already exists. Skipping access key creation."
else
    echo "Creating access keys for IAM user '$iam_user'..."
    keys_json=$(aws iam create-access-key --user-name "$iam_user")
    access_key=$(echo "$keys_json" | jq -r '.AccessKey.AccessKeyId')
    secret_key=$(echo "$keys_json" | jq -r '.AccessKey.SecretAccessKey')
    create_k8s_secret "$secret_name" "$access_key" "$secret_key"
fi

# Iterate over the buckets in S3_ROLES_JSON
echo "$S3_ROLES_JSON" | jq -c '.[]' | while read -r i; do
    bucket_name=$(echo "$i" | jq -r '.bucketName')
    acl=$(echo "$i" | jq -r '.acl')

    echo "Processing S3 bucket '$bucket_name' with ACL '$acl'..."
    create_s3_bucket "$bucket_name"
    create_s3_policy "$bucket_name" "$acl"

    policy_name="${bucket_name}_${acl}"
    policy_arn=$(aws iam list-policies --query "Policies[?PolicyName=='$policy_name'].Arn" --output text 2>/dev/null)

    if [[ -n "$policy_arn" && "$policy_arn" != "None" ]]; then
        echo "Attaching policy '$policy_name' to IAM user '$iam_user'..."
        attach_policy_to_user "$iam_user" "$policy_arn"
    else
        echo "Policy '$policy_name' does not exist, cannot attach to user '$iam_user'."
        
    fi
done

echo "S3 buckets, policies, IAM user, and access keys setup complete."

