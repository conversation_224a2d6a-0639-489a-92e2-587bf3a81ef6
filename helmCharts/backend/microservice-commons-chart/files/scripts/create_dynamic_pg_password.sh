#!/bin/bash
set -eo pipefail

# Environment variables from Helm
ENV="${ENVIRONMENT}"
SERVICE_NAME="${SERVICE_NAME}"
SECRET_NAME="${ENV}-${SERVICE_NAME}-postgres-pass"
NAMESPACE="${NAMESPACE}"
ENV_TYPE="${ENV_TYPE}"

secret_exists() {
    echo "Checking if Kubernetes secret '$1' exists in namespace $NAMESPACE..."
    if kubectl get secret "$1" -n "$NAMESPACE" >/dev/null 2>&1; then
        echo "Secret '$1' exists."
        return 0
    else
        echo "Secret '$1' not found."
        return 1
    fi
}

generate_password() {
    openssl rand -base64 16 | tr -dc 'a-zA-Z0-9' | head -c16
}

create_postgres_secret() {
    echo "Creating PostgreSQL secret..."
    local password=$(generate_password)
    
    # Create config content
    local config_content="${ENV_TYPE}.${ENV}.db.default.password = ${password}
${ENV_TYPE}.${ENV}.db.default.properties.password = ${password}"
    
    # Create the secret
    kubectl create secret generic "$SECRET_NAME" \
        --namespace "$NAMESPACE" \
        --from-literal=password="$password" \
        --from-literal=postgres-access-override.conf="$config_content" \
        --dry-run=client \
        -o yaml | kubectl apply -f -

    # Add Helm labels
    kubectl label secret -n "$NAMESPACE" "$SECRET_NAME" \
        app.kubernetes.io/managed-by=Helm
}

main() {
    if ! secret_exists "$SECRET_NAME"; then
        create_postgres_secret
    else
        echo "Secret $SECRET_NAME already exists - skipping creation"
    fi
}

main
