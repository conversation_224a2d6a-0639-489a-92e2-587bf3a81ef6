#!/bin/bash

# Enable debugging if the environment variable is set
if [[ "$SCRIPT_DEBUGGING_ENABLED" == "true" ]]; then
    echo "DEBUGGING ENABLED"
    set -x
fi

# Function to run scripts safely and log any failures
run_script() {
    local script_path=$1
    if [[ -f "$script_path" ]]; then
        echo "Running $script_path..."
        bash "$script_path"
        if [[ $? -ne 0 ]]; then
            echo "ERROR: Failed to execute $script_path"
            exit 1
        fi
    else
        echo "WARNING: $script_path not found, skipping."
    fi
}

# Update service endpoints configuration
# run_script /scripts/update-service-endpoints-configmap.sh


# Configure MongoDB if enabled
if [[ "$IS_MONGO_ENABLED" == "true" ]]; then
    echo "CONFIGURING MONGO..."
    run_script /scripts/mongo-service-init.sh
else
    echo "MongoDB configuration is disabled."
fi

# Configure Postgres if enabled
if [[ "$IS_PSQL_ENABLED" == "true" ]]; then
    echo "CONFIGURING POSTGRES..."
    run_script /scripts/psql-service-init.sh
else
    echo "Postgres configuration is disabled."
fi

if [[ "$IS_S3_ENABLED" == "true" ]]; then
    echo "CONFIGURING S3 for the service..."
    run_script /scripts/s3-service-init.sh
else
    echo "s3 configuration is disabled."
fi
echo "Configuration complete."
