#!/bin/bash
if [[ $SCRIPT_DEBUGGING_ENABLED == "true" ]]; then
    echo "DEBUGGING ENABLED"
    set -x
fi
# MongoDB Admin Credentials
MONGO_ADMIN_USER=$(cat /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret | jq -r ".mongo.\"${MONGO_HOST}\".admin_user")
MONGO_ADMIN_PASS=$(cat /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret | jq -r ".mongo.\"${MONGO_HOST}\".admin_password")

# MongoDB Service User Credentials
MONGO_SERVICE_PASS=$(cat /etc/mongo-service-secret/password)

mongo_user_exists() {
    local user=$1
    local db=$2
    echo "db.getUser('$user')" | mongo --host $MONGO_HOST --authenticationDatabase ${MON<PERSON><PERSON>_AUTHENTICATION_DATABASE} -u ${MONGO_ADMIN_USER} -p ${MONGO_ADMIN_PASS} --quiet | grep -q "\"$user\" :"
}

mongo_create_user() {
    local user=$1
    local pass=$2
    local roles=$3
    local db=$4
    if mongo_user_exists $user $db; then
        echo "MongoDB user $user already exists."
    else
        echo "Creating MongoDB user $user with roles $roles"
        echo "db.createUser({user: '$user', pwd: '$pass', roles: $roles})" | mongo --host $MONGO_HOST --authenticationDatabase ${MONGO_AUTHENTICATION_DATABASE} -u ${MONGO_ADMIN_USER} -p ${MONGO_ADMIN_PASS} $db
    fi
}

mongo_create_user "$MONGO_SERVICE_USER" "$MONGO_SERVICE_PASS" "$MONGO_ROLES_JSON" "$MONGO_DB_TO_CONNECT"
