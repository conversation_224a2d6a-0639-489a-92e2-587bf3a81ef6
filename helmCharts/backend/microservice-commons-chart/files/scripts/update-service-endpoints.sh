#!/bin/bash

################################################################################
# Update Service Endpoints in ConfigMap Script
#
# Description:
# This script updates the service endpoints in a specified ConfigMap for a 
# Kubernetes service. It checks whether the internal service endpoint exists in 
# the ConfigMap and adds it if not already present.
#
# Requirements:
# - The following environment variables must be set:
#     - `ENVIRONMENT`: The environment (e.g., "nsg1", "nalpha").
#     - `ENV_TYPE`: The environment type (e.g., "prod", "stage", etc.).
#     - `SERVICE_NAME`: The name of the service (e.g., "n-device-registration").
#     - `NAMESPACE`: The namespace where the service is running (e.g., "default").
#     - `SERVICE_ENDPOINTS_CONFIGMAP`: The name of the ConfigMap where service 
#       endpoints are stored.
#
# Debugging:
# - Set `SCRIPT_DEBUGGING_ENABLED=true` as an environment variable to enable 
#   debug mode. This will print each command being executed and additional 
#   details during script execution.
#
# Exit Codes:
# - 0: Success
# - 1: Failure (invalid environment variables or errors during Kubernetes operations).
################################################################################

# Enable script debugging if SCRIPT_DEBUGGING_ENABLED is set to true
if [[ $SCRIPT_DEBUGGING_ENABLED == "true" ]]; then
    echo "DEBUGGING ENABLED"
    set -x
fi

# Validate required environment variables
required_vars=("ENVIRONMENT" "ENV_TYPE" "SERVICE_NAME" "NAMESPACE" "SERVICE_ENDPOINTS_CONFIGMAP")
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "Error: $var is not set. Please ensure all required environment variables are provided."
        exit 1
    fi
done

# Construct the service internal endpoint
SERVICE_INTERNAL_ENDPOINT="${ENV_TYPE}.${ENVIRONMENT}.${SERVICE_NAME}-service-url = \"http://${ENVIRONMENT}-${SERVICE_NAME}.${NAMESPACE}.svc.cluster.local\""
SERVICE_ADMIN_ENDPOINT="${ENV_TYPE}.${ENVIRONMENT}.${SERVICE_NAME}-service-admin-external-url = \"https://${ENVIRONMENT}-${SERVICE_NAME}.admin.strawmine.com\""

# Reference:
# prod.nsg1.n-data-integrations-service-url="http://nsg1-n-data-integrations.ncinga.com"
# prod.nsg1.n-data-integrations-service-admin-external-url="https://nsg1-n-data-integrations.admin.ncinga.com"

# Fetch the current endpoints from the ConfigMap
echo "Fetching current endpoints from ConfigMap: $SERVICE_ENDPOINTS_CONFIGMAP"
CURRENT_ENDPOINTS=$(kubectl get configmap "$SERVICE_ENDPOINTS_CONFIGMAP" -o=jsonpath='{.data.endpoints}' 2>/dev/null)

# Check if the service internal endpoint is already in the ConfigMap
if echo "$CURRENT_ENDPOINTS" | grep -q "$SERVICE_INTERNAL_ENDPOINT"; then
    echo "Endpoint for $SERVICE_NAME already exists in the ConfigMap."
else
    echo "Adding endpoint for $SERVICE_NAME to the ConfigMap."

    # Append the new service endpoint to the existing ones
    if [[ -z "$CURRENT_ENDPOINTS" ]]; then
        echo "No existing endpoints found in the ConfigMap. Creating new entry."
        UPDATED_ENDPOINTS="${SERVICE_INTERNAL_ENDPOINT}"
    else
        echo "Appending the new service endpoint to the existing endpoints."
        UPDATED_ENDPOINTS="${CURRENT_ENDPOINTS}"$'\n'"${SERVICE_INTERNAL_ENDPOINT}"
    fi

    # Update the ConfigMap with the new data
    echo "Updating ConfigMap: $SERVICE_ENDPOINTS_CONFIGMAP with new endpoint."
    kubectl create configmap "$SERVICE_ENDPOINTS_CONFIGMAP" --from-literal=endpoints="$UPDATED_ENDPOINTS" --dry-run=client -o yaml | kubectl apply -f -
    
    # Verify the update was successful
    if [[ $? -ne 0 ]]; then
        echo "Error: Failed to update ConfigMap: $SERVICE_ENDPOINTS_CONFIGMAP."
        exit 1
    fi
fi

# Display the final list of service endpoints
CURRENT_ENDPOINTS=$(kubectl get configmap "$SERVICE_ENDPOINTS_CONFIGMAP" -o=jsonpath='{.data.endpoints}')
echo "Final list of service endpoints in ConfigMap $SERVICE_ENDPOINTS_CONFIGMAP:"
echo "$CURRENT_ENDPOINTS"

echo "END OF SCRIPT"
