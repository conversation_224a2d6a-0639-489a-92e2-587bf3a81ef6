#!/bin/bash

################################################################################
# PostgreSQL Service Initialization Script
#
# Description:
# This script automates the setup of a PostgreSQL service database and user(s).
# It performs the following tasks:
# 
# 1. **Environment and Credentials Setup**: 
#    - Retrieves PostgreSQL admin credentials and service user credentials from 
#      Kubernetes Secrets stored in `/etc/db-admin-secret` and `/etc/postgres-service-secret`.
#    - Exports required environment variables for PostgreSQL connections (`PGUSER`, 
#      `PGPASSWORD`, `PGHOST`, `PGPORT`, and `PGDATABASE`).
#
# 2. **Database and Role Management**:
#    - Checks if the service database (`PSQL_SERVICE_DB`) exists, and if not, creates it.
#    - Checks if the service user (`PSQL_SERVICE_USER`) exists, and if not, creates it with 
#      the specified password.
#    - Revokes default schema creation privileges from all users and grants schema creation 
#      privileges only to the service user.
#    - Creates additional roles (`_read`, `_read_ext`, `_write`) for different access levels, 
#      if they do not exist.
# 
# 3. **Privilege Management**:
#    - Grants the appropriate read and write privileges to the `_read`, `_read_ext`, and 
#      `_write` roles.
#    - Assigns the service user to the `_write` role, allowing the user to inherit write 
#      privileges for the database.
################################################################################


# Enable script debugging if SCRIPT_DEBUGGING_ENABLED is set to true
if [[ $SCRIPT_DEBUGGING_ENABLED == "true" ]]; then
    echo "DEBUGGING ENABLED"
    set -x
fi

# Ensure all necessary environment variables are set
required_vars=("PSQL_HOST" "PSQL_PORT" "ENVIRONMENT" "PSQL_SERVICE_USER" "PSQL_SERVICE_DB")
for var in "${required_vars[@]}"; do
  if [[ -z "${!var}" ]]; then
    echo "Error: $var is not set. Please ensure all required environment variables are provided."
    exit 1
  fi
done

# PostgreSQL Admin Credentials
PSQL_ADMIN_USER=$(cat /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret | jq -r ".postgres.\"${PSQL_HOST}\".admin_user")
PSQL_ADMIN_PASS=$(cat /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret | jq -r ".postgres.\"${PSQL_HOST}\".admin_password")

# PostgreSQL Service User Credentials
PSQL_SERVICE_PASS=$(cat /etc/postgres-service-secret/password)

# Ensure PostgreSQL credentials are successfully retrieved
if [[ -z "$PSQL_ADMIN_USER" || -z "$PSQL_ADMIN_PASS" ]]; then
    echo "Error: Failed to retrieve PostgreSQL admin credentials for host $PSQL_HOST."
    exit 1
fi
if [[ -z "$PSQL_SERVICE_PASS" ]]; then
    echo "Error: PostgreSQL service user password is not set."
    exit 1
fi

# Set environment variables for PostgreSQL connection
export PGUSER=$PSQL_ADMIN_USER
export PGPASSWORD=$PSQL_ADMIN_PASS
export PGHOST=$PSQL_HOST
export PGPORT=$PSQL_PORT

# Function to check if a PostgreSQL role exists
role_exists() {
    local role=$1
    echo "Checking if role '$role' exists..."
    psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$role'" | grep -q 1
    return $?
}

# Function to check if a PostgreSQL database exists
database_exists() {
    local db=$1
    echo "Checking if database '$db' exists..."
    psql -tAc "SELECT 1 FROM pg_database WHERE datname='$db'" | grep -q 1
    return $?
}

# Function to create a role if it doesn't exist
create_role_if_not_exists() {
    local role=$1
    if role_exists "$role"; then
        echo "Role '$role' already exists."
    else
        echo "Creating role '$role'."
        psql -c "CREATE ROLE $role;"
    fi
}

# Function to create a database if it doesn't exist
create_database_if_not_exists() {
    local db=$1
    if database_exists "$db"; then
        echo "Database '$db' already exists."
    else
        echo "Creating database '$db'."
        psql -c "CREATE DATABASE $db;"
    fi
}

# Function to grant read privileges to a user
alter_read_privileges() {
    local user=$1
    echo "Granting read privileges to user '$user'."
    psql -c "ALTER DEFAULT PRIVILEGES IN SCHEMA PUBLIC GRANT SELECT ON TABLES TO $user;"
    psql -c "ALTER DEFAULT PRIVILEGES IN SCHEMA PUBLIC GRANT SELECT ON SEQUENCES TO $user;"
    psql -c "GRANT SELECT ON ALL TABLES IN SCHEMA PUBLIC TO $user;"
    psql -c "GRANT SELECT ON ALL SEQUENCES IN SCHEMA PUBLIC TO $user;"
}

export PGDATABASE='postgres'
# Create the service database if it doesn't exist
create_database_if_not_exists "$PSQL_SERVICE_DB"

# Create the service user if it doesn't exist
if role_exists "$PSQL_SERVICE_USER"; then
    echo "User '$PSQL_SERVICE_USER' already exists."
else
    echo "Creating user '$PSQL_SERVICE_USER' with password."
    psql -c "CREATE USER $PSQL_SERVICE_USER WITH PASSWORD '${PSQL_SERVICE_PASS}';"
fi

# Connect to the service database
export PGDATABASE=$PSQL_SERVICE_DB

# Revoke default create permissions from schema public
echo "Revoking default create permissions from schema PUBLIC."
psql -c "REVOKE CREATE ON SCHEMA PUBLIC FROM PUBLIC;"
echo "Granting create permissions on schema PUBLIC to '$PSQL_SERVICE_USER'."
psql -c "GRANT CREATE ON SCHEMA PUBLIC TO $PSQL_SERVICE_USER;"

# Create roles if they do not exist
create_role_if_not_exists "${PSQL_SERVICE_USER}_read"
create_role_if_not_exists "${PSQL_SERVICE_USER}_read_ext"
create_role_if_not_exists "${PSQL_SERVICE_USER}_write"

# Grant privileges to read roles
alter_read_privileges "${PSQL_SERVICE_USER}_read"
alter_read_privileges "${PSQL_SERVICE_USER}_read_ext"

# Grant the write role privileges
echo "Granting write privileges to role '${PSQL_SERVICE_USER}_write'."
psql -c "GRANT $PSQL_SERVICE_USER TO ${PSQL_SERVICE_USER}_write;"

echo "PostgreSQL setup complete for user '$PSQL_SERVICE_USER' and database '$PSQL_SERVICE_DB'."
