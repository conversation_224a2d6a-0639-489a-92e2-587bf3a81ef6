#!/bin/bash
set -eo pipefail

# Environment variables from Helm
ENV="${ENVIRONMENT}"
SERVICE_NAME="${SERVICE_NAME}"
SECRET_NAME="${ENV}-${SERVICE_NAME}-mongo-pass"
NAMESPACE="${NAMESPACE}"

secret_exists() {
    echo "Checking if Kubernetes secret '$1' exists in namespace $NAMESPACE..."
    if kube<PERSON><PERSON> get secret "$1" -n "$NAMESPACE" >/dev/null 2>&1; then
        echo "Secret '$1' exists."
        return 0
    else
        echo "Secret '$1' not found."
        return 1
    fi
}

generate_password() {
    openssl rand -base64 16 | tr -dc 'a-zA-Z0-9' | head -c16
}

create_mongo_secret() {
    echo "Creating MongoDB secret..."
    local password=$(generate_password)
    
    # Create the secret with just the password
    kubectl create secret generic "$SECRET_NAME" \
        --namespace "$NAMESPACE" \
        --from-literal=password="$password" \
        --dry-run=client \
        -o yaml | kubectl apply -f -

    # Add <PERSON><PERSON> labels
    kubectl label secret -n "$NAMESPACE" "$SECRET_NAME" \
        app.kubernetes.io/managed-by=Helm
}

main() {
    if ! secret_exists "$SECRET_NAME"; then
        create_mongo_secret
    else
        echo "Secret $SECRET_NAME already exists - skipping creation"
    fi
}

main
