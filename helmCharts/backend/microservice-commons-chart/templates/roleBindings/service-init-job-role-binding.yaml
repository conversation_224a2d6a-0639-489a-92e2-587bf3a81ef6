apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: backend-service-init-job-role-binding
  labels:
    {{- include "microservice-commons-chart.labels" . | nindent 4 }}
  namespace: {{ .Release.Namespace }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: backend-service-init-job-role
subjects:
  - kind: ServiceAccount
    name: backend-service-init-job-sa
    namespace: {{ .Release.Namespace }}