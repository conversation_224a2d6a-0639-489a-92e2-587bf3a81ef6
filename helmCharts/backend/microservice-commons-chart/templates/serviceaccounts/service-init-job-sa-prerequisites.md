#!/bin/bash
# Run this script with AWS IAM access to create IAM roles and policies required by the Helm chart.

# Load values from <PERSON><PERSON> to make the script reusable for different environments
NAMESPACE="stage-backend"
SERVICE_ACCOUNT_NAME="backend-service-chart-init-job-sa"
CLUSTER_NAME="staging-solvei8-eks"
ROLE_NAME="Backend-Service-Init-Pod-Identity-Role"
POLICY_NAME="Backend-Service-Init-Pod-Identity-Policy"

# Extracting AWS Account ID
ACCOUNT_ID=$(aws sts get-caller-identity --query "Account" --output text)

# Step 1: Create the IAM Role with Trust Policy
cat <<EOF > Service-Init-Role-Trust-Policy.json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "pods.eks.amazonaws.com"
      },
      "Action": [
        "sts:AssumeRole",
        "sts:TagSession"
      ]
    }
  ]
}
EOF

aws iam create-role \
  --role-name ${ROLE_NAME} \
  --assume-role-policy-document file://Service-Init-Role-Trust-Policy.json

# Step 2: Create the S3 Access Policy
cat <<EOF > Service-Init-Pod-Identity-Policy.json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListAllMyBuckets",
                "s3:GetBucketLocation",
                "s3:CreateBucket",
                "s3:PutBucketPolicy",
                "s3:GetBucketPolicy",
                "s3:ListBucket"
            ],
            "Resource": "arn:aws:s3:::*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "iam:CreateUser",
                "iam:ListUsers",
                "iam:GetUser",
                "iam:AttachUserPolicy",
                "iam:DetachUserPolicy",
                "iam:PutUserPolicy",
                "iam:ListAttachedUserPolicies",
                "iam:CreateAccessKey",
                "iam:ListAccessKeys"
            ],
            "Resource": "arn:aws:iam::${ACCOUNT_ID}:user/*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "iam:CreatePolicy",
                "iam:GetPolicy",
                "iam:ListPolicies",
                "iam:AttachUserPolicy"
            ],
            "Resource": "*"
        }
    ]
}
EOF

# Create the policy
aws iam create-policy \
  --policy-name ${POLICY_NAME} \
  --policy-document file://Service-Init-Pod-Identity-Policy.json

# Attach the policy to the role
aws iam attach-role-policy \
  --role-name ${ROLE_NAME} \
  --policy-arn arn:aws:iam::${ACCOUNT_ID}:policy/${POLICY_NAME}

# Associate the IAM role with the Kubernetes service account by creating a Pod Identity Association.
aws eks create-pod-identity-association \
  --cluster-name ${CLUSTER_NAME} \
  --namespace ${NAMESPACE} \
  --service-account ${SERVICE_ACCOUNT_NAME} \
  --role-arn arn:aws:iam::${ACCOUNT_ID}:role/${ROLE_NAME}

# Step: Cleanup the resources on local to keep it clean
rm Service-Init-Role-Trust-Policy.json Service-Init-Pod-Identity-Policy.json

# You can list the pod identity associations with
aws eks list-pod-identity-associations --cluster-name staging-solvei8-eks