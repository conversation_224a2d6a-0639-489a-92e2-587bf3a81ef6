apiVersion: v1
kind: ConfigMap
metadata:
  name: override-service-endpoints-external
  labels:
    {{- include "microservice-commons-chart.labels" . | nindent 4 }}
data:
  override-service-endpoints-external: |
{{- range $service := .Values.externalServiceNames }}
    {{- $urlSuffix := (index $.Values.externalServiceUrls $service | default $service) }}
    {{- if contains "." $urlSuffix }}
      {{ $service }}-external-host-url="{{ $urlSuffix }}{{ $.Values.hostnameSuffix }}"
    {{- else }}
      {{ $service }}-external-host-url="{{ $.Values.environment }}-{{ $urlSuffix }}{{ $.Values.hostnameSuffix }}"
    {{- end }}
{{- end }}
