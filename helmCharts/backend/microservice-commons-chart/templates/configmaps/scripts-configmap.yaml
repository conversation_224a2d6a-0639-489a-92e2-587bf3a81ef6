apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-scripts-configmap
  labels:
    {{- include "microservice-commons-chart.labels" . | nindent 4 }}
data:
  create_dynamic_mongo_password.sh: |
{{ .Files.Get "files/scripts/create_dynamic_mongo_password.sh" | indent 4 }}

  create_dynamic_pg_password.sh: |
{{ .Files.Get "files/scripts/create_dynamic_pg_password.sh" | indent 4 }}

  psql-service-init.sh: |
{{ .Files.Get "files/scripts/psql-service-init.sh" | indent 4 }}

  mongo-service-init.sh: |
{{ .Files.Get "files/scripts/mongo-service-init.sh" | indent 4 }}

  s3-service-init.sh: |
{{ .Files.Get "files/scripts/s3-service-init.sh" | indent 4 }}

  configure-service.sh: |
{{ .Files.Get "files/scripts/configure-service.sh" | indent 4 }}

  create_dynamic_environment_variables.sh: |
{{ .Files.Get "files/scripts/create_dynamic_environment_variables.sh" | indent 4 }}