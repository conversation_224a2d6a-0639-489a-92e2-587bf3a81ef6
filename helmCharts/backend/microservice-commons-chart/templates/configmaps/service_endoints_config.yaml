{{- $envType := .Values.envType -}}
{{- $env := .Values.environment -}}
{{- $internalDomain := printf "%s.svc.cluster.local" .Release.Namespace -}}
{{- $adminDomain := printf "admin%s" .Values.hostnameSuffix -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: override-service-endpoints
  labels:
    {{- include "microservice-commons-chart.labels" . | nindent 4 }}
data:
  override-service-endpoints: |
{{- range $service := .Values.dependentServiceNames }}
    {{ $envType }}.{{ $env }}.{{ $service }}-service-url="http://{{ $env }}-{{ $service }}.{{ $internalDomain }}"
    {{ $envType }}.{{ $env }}.{{ $service }}-service-admin-external-url="http://{{ $env }}-{{ $service }}.{{ $adminDomain }}"
{{- end }}
