---
# Source: microservice-commons-chart/templates/serviceaccounts/service-init-job-sa.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: backend-service-init-job-sa
  labels:
    helm.sh/chart: microservice-commons-chart-0.1.0
    app.kubernetes.io/name: microservice-commons-chart
    app.kubernetes.io/instance: commons
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
---
# Source: microservice-commons-chart/templates/configmaps/jmx_prometheus_config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: jmx-prometheus-config-configmap
  labels:
    helm.sh/chart: microservice-commons-chart-0.1.0
    app.kubernetes.io/name: microservice-commons-chart
    app.kubernetes.io/instance: commons
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
data:
  jmx_prometheus_config.yaml: |
    # lowercaseOutputName: true
    # lowercaseOutputLabelNames: true
    rules:
      - pattern: ".*"
---
# Source: microservice-commons-chart/templates/configmaps/nginx.conf.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-conf-configmap
  labels:
    helm.sh/chart: microservice-commons-chart-0.1.0
    app.kubernetes.io/name: microservice-commons-chart
    app.kubernetes.io/instance: commons
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
data:
  nginx.conf: |
    user  nginx;
    worker_processes  auto;
    
    error_log  /var/log/nginx/error.log notice;
    pid        /var/run/nginx.pid;
    
    events {
        worker_connections  1024;
    }
    
    http {
        include       /etc/nginx/mime.types;
        default_type  application/octet-stream;
    
        log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                            '$status $body_bytes_sent "$http_referer" '
                            '"$http_user_agent" "$http_x_forwarded_for"';
    
        access_log  /var/log/nginx/access.log  main;
    
        sendfile        on;
        keepalive_timeout  65;
    
        # Define maps for connection upgrades and keep-alive
        map $http_upgrade $connection_upgrade {
            default          upgrade;
            ''               close;
        }
    
        map $http_upgrade $keepalive_connection_upgrade {
            default          upgrade;
            ''               '';
        }
    
        include /etc/nginx/conf.d/*.conf;
    }
---
# Source: microservice-commons-chart/templates/configmaps/nginxConfs.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-configs-configmap  # Use chart name instead of release name
  labels:
    helm.sh/chart: microservice-commons-chart-0.1.0
    app.kubernetes.io/name: microservice-commons-chart
    app.kubernetes.io/instance: commons
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
data:
  n-machine-maintenance-web.conf: |
    server {
        listen 80 default_server;  # Listen on port 80 and set this block as the default
        # Set headers to pass through relevant request details to the proxied server
        proxy_set_header X-Host $http_host;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-Host $http_x_forwarded_host;
        proxy_set_header X-Real-IP $remote_addr;  # Use $remote_addr to pass the correct client IP
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        
        # Ensure HTTP/1.1 is used for chunked responses
        proxy_http_version 1.1;
    
        # For WebSockets or other upgrade scenarios
        proxy_set_header Upgrade $http_upgrade;
        
        # This may need to be adjusted to ensure keepalive connections work properly:
        proxy_set_header Connection $http_connection;
    
        # Serve the static frontend content
        location / {
            try_files $uri $uri/ /index.html;  # Ensure single-page app fallback to index.html
            root /app/www;  # Ensure /app/www contains your static files
            client_max_body_size 50M;  # Adjust as necessary for file uploads
    
            # Proxy settings
            proxy_send_timeout 300;
            proxy_read_timeout 300;
        }
    }
---
# Source: microservice-commons-chart/templates/configmaps/scripts-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-scripts-configmap
  labels:
    helm.sh/chart: microservice-commons-chart-0.1.0
    app.kubernetes.io/name: microservice-commons-chart
    app.kubernetes.io/instance: commons
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
data:
  psql-service-init.sh: |
    #!/bin/bash
    
    ################################################################################
    # PostgreSQL Service Initialization Script
    #
    # Description:
    # This script automates the setup of a PostgreSQL service database and user(s).
    # It performs the following tasks:
    # 
    # 1. **Environment and Credentials Setup**: 
    #    - Retrieves PostgreSQL admin credentials and service user credentials from 
    #      Kubernetes Secrets stored in `/etc/db-admin-secret` and `/etc/postgres-service-secret`.
    #    - Exports required environment variables for PostgreSQL connections (`PGUSER`, 
    #      `PGPASSWORD`, `PGHOST`, `PGPORT`, and `PGDATABASE`).
    #
    # 2. **Database and Role Management**:
    #    - Checks if the service database (`PSQL_SERVICE_DB`) exists, and if not, creates it.
    #    - Checks if the service user (`PSQL_SERVICE_USER`) exists, and if not, creates it with 
    #      the specified password.
    #    - Revokes default schema creation privileges from all users and grants schema creation 
    #      privileges only to the service user.
    #    - Creates additional roles (`_read`, `_read_ext`, `_write`) for different access levels, 
    #      if they do not exist.
    # 
    # 3. **Privilege Management**:
    #    - Grants the appropriate read and write privileges to the `_read`, `_read_ext`, and 
    #      `_write` roles.
    #    - Assigns the service user to the `_write` role, allowing the user to inherit write 
    #      privileges for the database.
    ################################################################################
    
    
    # Enable script debugging if SCRIPT_DEBUGGING_ENABLED is set to true
    if [[ $SCRIPT_DEBUGGING_ENABLED == "true" ]]; then
        echo "DEBUGGING ENABLED"
        set -x
    fi
    
    # Ensure all necessary environment variables are set
    required_vars=("PSQL_HOST" "PSQL_PORT" "ENVIRONMENT" "PSQL_SERVICE_USER" "PSQL_SERVICE_DB")
    for var in "${required_vars[@]}"; do
      if [[ -z "${!var}" ]]; then
        echo "Error: $var is not set. Please ensure all required environment variables are provided."
        exit 1
      fi
    done
    
    # PostgreSQL Admin Credentials
    PSQL_ADMIN_USER=$(cat /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret | jq -r ".postgres.\"${PSQL_HOST}\".admin_user")
    PSQL_ADMIN_PASS=$(cat /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret | jq -r ".postgres.\"${PSQL_HOST}\".admin_password")
    
    # PostgreSQL Service User Credentials
    PSQL_SERVICE_PASS=$(cat /etc/postgres-service-secret/password)
    
    # Ensure PostgreSQL credentials are successfully retrieved
    if [[ -z "$PSQL_ADMIN_USER" || -z "$PSQL_ADMIN_PASS" ]]; then
        echo "Error: Failed to retrieve PostgreSQL admin credentials for host $PSQL_HOST."
        exit 1
    fi
    if [[ -z "$PSQL_SERVICE_PASS" ]]; then
        echo "Error: PostgreSQL service user password is not set."
        exit 1
    fi
    
    # Set environment variables for PostgreSQL connection
    export PGUSER=$PSQL_ADMIN_USER
    export PGPASSWORD=$PSQL_ADMIN_PASS
    export PGHOST=$PSQL_HOST
    export PGPORT=$PSQL_PORT
    
    # Function to check if a PostgreSQL role exists
    role_exists() {
        local role=$1
        echo "Checking if role '$role' exists..."
        psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$role'" | grep -q 1
        return $?
    }
    
    # Function to check if a PostgreSQL database exists
    database_exists() {
        local db=$1
        echo "Checking if database '$db' exists..."
        psql -tAc "SELECT 1 FROM pg_database WHERE datname='$db'" | grep -q 1
        return $?
    }
    
    # Function to create a role if it doesn't exist
    create_role_if_not_exists() {
        local role=$1
        if role_exists "$role"; then
            echo "Role '$role' already exists."
        else
            echo "Creating role '$role'."
            psql -c "CREATE ROLE $role;"
        fi
    }
    
    # Function to create a database if it doesn't exist
    create_database_if_not_exists() {
        local db=$1
        if database_exists "$db"; then
            echo "Database '$db' already exists."
        else
            echo "Creating database '$db'."
            psql -c "CREATE DATABASE $db;"
        fi
    }
    
    # Function to grant read privileges to a user
    alter_read_privileges() {
        local user=$1
        echo "Granting read privileges to user '$user'."
        psql -c "ALTER DEFAULT PRIVILEGES IN SCHEMA PUBLIC GRANT SELECT ON TABLES TO $user;"
        psql -c "ALTER DEFAULT PRIVILEGES IN SCHEMA PUBLIC GRANT SELECT ON SEQUENCES TO $user;"
        psql -c "GRANT SELECT ON ALL TABLES IN SCHEMA PUBLIC TO $user;"
        psql -c "GRANT SELECT ON ALL SEQUENCES IN SCHEMA PUBLIC TO $user;"
    }
    
    export PGDATABASE='postgres'
    # Create the service database if it doesn't exist
    create_database_if_not_exists "$PSQL_SERVICE_DB"
    
    # Create the service user if it doesn't exist
    if role_exists "$PSQL_SERVICE_USER"; then
        echo "User '$PSQL_SERVICE_USER' already exists."
    else
        echo "Creating user '$PSQL_SERVICE_USER' with password."
        psql -c "CREATE USER $PSQL_SERVICE_USER WITH PASSWORD '${PSQL_SERVICE_PASS}';"
    fi
    
    # Connect to the service database
    export PGDATABASE=$PSQL_SERVICE_DB
    
    # Revoke default create permissions from schema public
    echo "Revoking default create permissions from schema PUBLIC."
    psql -c "REVOKE CREATE ON SCHEMA PUBLIC FROM PUBLIC;"
    echo "Granting create permissions on schema PUBLIC to '$PSQL_SERVICE_USER'."
    psql -c "GRANT CREATE ON SCHEMA PUBLIC TO $PSQL_SERVICE_USER;"
    
    # Create roles if they do not exist
    create_role_if_not_exists "${PSQL_SERVICE_USER}_read"
    create_role_if_not_exists "${PSQL_SERVICE_USER}_read_ext"
    create_role_if_not_exists "${PSQL_SERVICE_USER}_write"
    
    # Grant privileges to read roles
    alter_read_privileges "${PSQL_SERVICE_USER}_read"
    alter_read_privileges "${PSQL_SERVICE_USER}_read_ext"
    
    # Grant the write role privileges
    echo "Granting write privileges to role '${PSQL_SERVICE_USER}_write'."
    psql -c "GRANT $PSQL_SERVICE_USER TO ${PSQL_SERVICE_USER}_write;"
    
    echo "PostgreSQL setup complete for user '$PSQL_SERVICE_USER' and database '$PSQL_SERVICE_DB'."
    

  mongo-service-init.sh: |
    #!/bin/bash
    
    ################################################################################
    # MongoDB Service Initialization Script
    #
    # Description:
    # This script automates the setup of MongoDB users by managing user accounts
    # and assigning roles in a MongoDB database environment. The following
    # functionalities are provided:
    #
    # 1. **Environment and Credentials Setup**:
    #    - Retrieves MongoDB admin and service user credentials from Kubernetes Secrets.
    #    - Credentials are stored in `/etc/db-admin-secret` and `/etc/mongo-service-secret`.
    #    - Sets necessary environment variables to connect to the MongoDB instance 
    #      (`MONGO_HOST`, `MONGO_ADMIN_USER`, `MONGO_ADMIN_PASS`, etc.).
    #
    # 2. **User and Role Management**:
    #    - Checks if a MongoDB service user (`MONGO_SERVICE_USER`) already exists 
    #      in the specified target database (`MONGO_DB_TO_CONNECT`).
    #    - If the user does not exist, it creates the user with the given password 
    #      and roles (`MONGO_ROLES_JSON`).
    #    - If the user already exists but has different roles, it updates the roles
    #      to match the provided roles (`MONGO_ROLES_JSON`).
    #
    # 3. **User Verification**:
    #    - Provides feedback on the operation's success or failure after creating or
    #      updating a user.
    #
    # 4. **Databases for User Creation**:
    #    - Development and global access users are created in the admin database.
    #    - Service users are created in the `NSP_Users` database.
    #
    # Usage:
    # To execute this script, ensure that all the required environment variables are set.
    # - `MONGO_HOST`: The MongoDB host to connect to.
    # - `ENVIRONMENT`: The environment context (e.g., "staging", "production") for secrets.
    # - `MONGO_AUTHENTICATION_DATABASE`: The authentication database (e.g., "NSP_Users").
    # - `MONGO_SERVICE_USER`: The service user that will be managed or created.
    # - `MONGO_ROLES_JSON`: The roles to assign to the user (in JSON format).
    #
    # Example of `MONGO_ROLES_JSON`:
    # '[{"db":"orders","role":"readWrite"},{"db":"sales","role":"read"}]'
    #
    ################################################################################
    
    # Enable script debugging if SCRIPT_DEBUGGING_ENABLED is set to true
    if [[ $SCRIPT_DEBUGGING_ENABLED == "true" ]]; then
        echo "DEBUGGING ENABLED"
        set -x
    fi
    
    # Check if necessary environment variables are set
    required_vars=("MONGO_HOST" "ENVIRONMENT" "MONGO_AUTHENTICATION_DATABASE" "MONGO_SERVICE_USER" "MONGO_ROLES_JSON")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            echo "Error: $var is not set. Please ensure all required environment variables are provided."
            exit 1
        fi
    done
    
    # Retrieve MongoDB Admin Credentials
    MONGO_ADMIN_USER=$(jq -r ".mongo.\"${MONGO_HOST}\".admin_user" < /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret)
    MONGO_ADMIN_PASS=$(jq -r ".mongo.\"${MONGO_HOST}\".admin_password" < /etc/db-admin-secret/${ENVIRONMENT}-db-admin-secret)
    
    # Check if the MongoDB Admin Credentials were successfully retrieved
    if [[ -z "$MONGO_ADMIN_USER" || -z "$MONGO_ADMIN_PASS" ]]; then
        echo "Error: Failed to retrieve MongoDB admin credentials for host $MONGO_HOST."
        exit 1
    fi
    
    # Retrieve MongoDB Service User Credentials
    MONGO_SERVICE_PASS=$(cat /etc/mongo-service-secret/password)
    if [[ -z "$MONGO_SERVICE_PASS" ]]; then
        echo "Error: MongoDB service user password is not set."
        exit 1
    fi
    
    # Function to check if a MongoDB user exists in a given database
    # Returns 0 if the user exists, 1 otherwise
    mongo_user_exists() {
        local user=$1
        local user_db=$2
        echo "Checking if user '$user' exists in database '$user_db'..."
    
        mongo --host "$MONGO_HOST"   --authenticationDatabase "admin" -u "$MONGO_ADMIN_USER" -p "$MONGO_ADMIN_PASS" --quiet <<EOF
    use ${user_db}
    var user = db.getUser('$user');
    if (user) {
        quit(0);
    } else {
        quit(1);
    }
    EOF
        return $?
    }
    
    # Function to get the roles of a MongoDB user
    # Outputs the roles in JSON format
    mongo_get_user_roles() {
        local user=$1
        local user_db=$2
    
        mongo --host "$MONGO_HOST"   --authenticationDatabase "admin" -u "$MONGO_ADMIN_USER" -p "$MONGO_ADMIN_PASS" --quiet <<EOF | grep -E '^\[|^\{'
    use ${user_db}
    var user = db.getUser('$user');
    if (user) {
        print(JSON.stringify(user.roles));
        quit(0);
    } else {
        quit(1);
    }
    EOF
    }
    
    # Function to create or update a MongoDB user with specified roles in a given database
    mongo_create_or_update_user() {
        local user=$1
        local pass=$2
        local roles=$3
        local user_db=$4
    
        if mongo_user_exists "$user" "$user_db"; then
            echo "MongoDB user '$user' already exists in database '$user_db'."
    
            # Get existing roles of the user
            existing_roles=$(mongo_get_user_roles "$user" "$user_db")
            if [[ $? -ne 0 ]]; then
                echo "Error: Failed to retrieve roles for user '$user'."
                exit 1
            fi
    
            # Normalize JSON strings for comparison using jq
            normalized_existing_roles=$(echo "$existing_roles" | jq -c 'sort_by(.db, .role)' 2>/dev/null)
            normalized_provided_roles=$(echo "$roles" | jq -c 'sort_by(.db, .role)')
    
            # Check if jq returned an error for existing roles
            if [[ -z "$normalized_existing_roles" ]]; then
                echo "Error: Failed to parse existing roles for user '$user'."
                exit 1
            fi
    
            # Compare the two JSON role lists using jq
            if echo "$normalized_existing_roles" | jq --argjson roles "$normalized_provided_roles" -e 'if . == $roles then true else false end' >/dev/null; then
                echo "Roles for user '$user' are already up-to-date."
            else
                echo "Updating roles for user '$user' in database '$user_db'..."
                mongo --host "$MONGO_HOST"   --authenticationDatabase "admin" -u "$MONGO_ADMIN_USER" -p "$MONGO_ADMIN_PASS" <<EOF
    use ${user_db}
    db.updateUser("$user", { roles: $roles })
    EOF
    
                if [[ $? -ne 0 ]]; then
                    echo "Error: Failed to update roles for user '$user'."
                    exit 1
                else
                    echo "Roles for user '$user' updated successfully."
                fi
            fi
        else
            echo "Creating MongoDB user '$user' in database '$user_db' with roles: $roles"
    
            mongo --host "$MONGO_HOST"   --authenticationDatabase "admin" -u "$MONGO_ADMIN_USER" -p "$MONGO_ADMIN_PASS" <<EOF
    use ${user_db}
    db.createUser({user: "$user", pwd: "$pass", roles: $roles})
    EOF
    
            if [[ $? -ne 0 ]]; then
                echo "Error: Failed to execute MongoDB command to create user '$user'."
                exit 1
            fi
    
            # Check if the user creation was successful
            if mongo_user_exists "$user" "$user_db"; then
                echo "MongoDB user '$user' created successfully in database '$user_db'."
            else
                echo "Error: Failed to create MongoDB user '$user' in database '$user_db'."
                exit 1
            fi
        fi
    }
    
    # Run the function to create or update the MongoDB service user
    mongo_create_or_update_user "$MONGO_SERVICE_USER" "$MONGO_SERVICE_PASS" "$MONGO_ROLES_JSON" "$MONGO_AUTHENTICATION_DATABASE"
    

  s3-service-init.sh: |
    #!/bin/bash
    
    ################################################################################
    # Script: s3-service-init.sh
    # Author: <Your Name or Company>
    # Purpose:
    # Automates the setup of AWS S3 buckets, IAM policies, and Kubernetes secrets
    # for services that interact with S3. Ensures that required S3 buckets, IAM users,
    # and access policies are in place, and securely stores access keys in Kubernetes secrets.
    ################################################################################
    
    # Enable debugging if specified
    if [[ $SCRIPT_DEBUGGING_ENABLED == "true" ]]; then
        echo "DEBUGGING ENABLED"
        set -x
    fi
    
    # Set AWS region with fallback
    export AWS_REGION=${S3_REGION:-'ap-southeast-1'}
    
    # Function to create an S3 bucket if it doesn't exist
    create_s3_bucket() {
        local bucket_name=$1
        echo "Checking if S3 bucket '$bucket_name' exists..."
        if ! aws s3 ls "s3://$bucket_name" --region "$AWS_REGION" >/dev/null 2>&1; then
            echo "Bucket '$bucket_name' does not exist. Creating..."
            aws s3api create-bucket --bucket "$bucket_name" --region "$AWS_REGION" --create-bucket-configuration LocationConstraint="$AWS_REGION" 2>/dev/null
            if [[ $? -eq 0 ]]; then
                echo "Bucket '$bucket_name' created successfully."
            else
                echo "Error occurred while creating bucket '$bucket_name'."
            fi
        else
            echo "Bucket '$bucket_name' already exists."
        fi
    }
    
    # Function to create IAM policy for S3 bucket
    create_s3_policy() {
        local bucket_name=$1
        local acl=$2
        local policy_name="${bucket_name}_${acl}"
        echo "Checking if IAM policy '$policy_name' exists..."
        local policy_arn=$(aws iam list-policies --query "Policies[?PolicyName=='$policy_name'].Arn" --output text 2>/dev/null)
    
        if [[ -z "$policy_arn" || "$policy_arn" == "None" ]]; then
            echo "Policy '$policy_name' does not exist. Creating..."
            local policy_document
            if [[ "$acl" == "read" ]]; then
                policy_document='{
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Action": ["s3:GetObject", "s3:ListBucket"],
                            "Resource": ["arn:aws:s3:::'"$bucket_name"'", "arn:aws:s3:::'"$bucket_name"'/*"]
                        }
                    ]
                }'
            elif [[ "$acl" == "write" ]]; then
                policy_document='{
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Action": ["s3:GetObject", "s3:ListBucket", "s3:PutObject", "s3:DeleteObject"],
                            "Resource": ["arn:aws:s3:::'"$bucket_name"'", "arn:aws:s3:::'"$bucket_name"'/*"]
                        }
                    ]
                }'
            fi
    
            aws iam create-policy --policy-name "$policy_name" --policy-document "$policy_document" 2>/dev/null
            if [[ $? -eq 0 ]]; then
                echo "Policy '$policy_name' created successfully."
            else
                echo "Error occurred while creating policy '$policy_name'."
            fi
        else
            echo "Policy '$policy_name' already exists."
        fi
    }
    
    # Function to create an IAM user if it doesn't exist
    create_iam_user() {
        local iam_user=$1
        echo "Checking if IAM user '$iam_user' exists..."
        aws iam get-user --user-name "$iam_user" --output text >/dev/null 2>&1
        if [[ $? -ne 0 ]]; then
            echo "IAM user '$iam_user' does not exist. Creating..."
            aws iam create-user --user-name "$iam_user"
            if [[ $? -eq 0 ]]; then
                echo "IAM user '$iam_user' created successfully."
            else
                echo "Error occurred while creating IAM user '$iam_user'. Exiting."
                exit 1
            fi
        else
            echo "IAM user '$iam_user' already exists."
        fi
    }
    
    # Function to attach a policy to IAM user if not already attached
    attach_policy_to_user() {
        local iam_user=$1
        local policy_arn=$2
        echo "Checking if policy '$policy_arn' is attached to user '$iam_user'..."
        local policy_attached=$(aws iam list-attached-user-policies --user-name "$iam_user" --query "AttachedPolicies[?PolicyArn=='$policy_arn'].PolicyArn" --output text 2>/dev/null)
    
        if [[ -z "$policy_attached" || "$policy_attached" == "None" ]]; then
            echo "Attaching policy '$policy_arn' to user '$iam_user'..."
            aws iam attach-user-policy --user-name "$iam_user" --policy-arn "$policy_arn"
            if [[ $? -eq 0 ]]; then
                echo "Policy '$policy_arn' attached to user '$iam_user' successfully."
            else
                echo "Error occurred while attaching policy '$policy_arn' to user '$iam_user'."
            fi
        else
            echo "Policy '$policy_arn' is already attached to user '$iam_user'."
        fi
        
    }
    
    # Function to check if Kubernetes secret exists
    secret_exists() {
        local secret_name=$1
        echo "Checking if Kubernetes secret '$secret_name' exists..."
        if kubectl get secret "$secret_name" >/dev/null 2>&1; then
            echo "Kubernetes secret '$secret_name' exists."
            return 0
        else
            echo "Kubernetes secret '$secret_name' does not exist."
            return 1
        fi
    }
    
    # Function to create a Kubernetes secret
    create_k8s_secret() {
        local secret_name=$1
        local access_key=$2
        local secret_key=$3
        local config_content=$(cat <<EOF
    ${ENV_TYPE}.${ENVIRONMENT}.aws.s3 {
        access-key-id : "$access_key"
        secret-access-key : "$secret_key"
        region : "$AWS_REGION"
    }
    EOF
        )
        echo "Creating Kubernetes secret '$secret_name'..."
        kubectl create secret generic "$secret_name" --from-literal=s3-access-override.conf="$config_content"
        if [[ $? -eq 0 ]]; then
            echo "Kubernetes secret '$secret_name' created successfully."
        else
            echo "Error occurred while creating Kubernetes secret '$secret_name'."
        fi
        
    }
    
    # MAIN FLOW
    echo "Starting S3 Service Initialization..."
    iam_user="${ENV_TYPE}-${ENVIRONMENT}-${SERVICE_NAME}-s3-user"
    create_iam_user "$iam_user"
    
    secret_name="${ENVIRONMENT}-${SERVICE_NAME}-s3-credentials"
    if secret_exists "$secret_name"; then
        echo "Kubernetes secret '$secret_name' already exists. Skipping access key creation."
    else
        echo "Creating access keys for IAM user '$iam_user'..."
        keys_json=$(aws iam create-access-key --user-name "$iam_user")
        access_key=$(echo "$keys_json" | jq -r '.AccessKey.AccessKeyId')
        secret_key=$(echo "$keys_json" | jq -r '.AccessKey.SecretAccessKey')
        create_k8s_secret "$secret_name" "$access_key" "$secret_key"
    fi
    
    # Iterate over the buckets in S3_ROLES_JSON
    echo "$S3_ROLES_JSON" | jq -c '.[]' | while read -r i; do
        bucket_name=$(echo "$i" | jq -r '.bucketName')
        acl=$(echo "$i" | jq -r '.acl')
    
        echo "Processing S3 bucket '$bucket_name' with ACL '$acl'..."
        create_s3_bucket "$bucket_name"
        create_s3_policy "$bucket_name" "$acl"
    
        policy_name="${bucket_name}_${acl}"
        policy_arn=$(aws iam list-policies --query "Policies[?PolicyName=='$policy_name'].Arn" --output text 2>/dev/null)
    
        if [[ -n "$policy_arn" && "$policy_arn" != "None" ]]; then
            echo "Attaching policy '$policy_name' to IAM user '$iam_user'..."
            attach_policy_to_user "$iam_user" "$policy_arn"
        else
            echo "Policy '$policy_name' does not exist, cannot attach to user '$iam_user'."
            
        fi
    done
    
    echo "S3 buckets, policies, IAM user, and access keys setup complete."
    
    

  configure-service.sh: |
    #!/bin/bash
    
    # Enable debugging if the environment variable is set
    if [[ "$SCRIPT_DEBUGGING_ENABLED" == "true" ]]; then
        echo "DEBUGGING ENABLED"
        set -x
    fi
    
    # Function to run scripts safely and log any failures
    run_script() {
        local script_path=$1
        if [[ -f "$script_path" ]]; then
            echo "Running $script_path..."
            bash "$script_path"
            if [[ $? -ne 0 ]]; then
                echo "ERROR: Failed to execute $script_path"
                exit 1
            fi
        else
            echo "WARNING: $script_path not found, skipping."
        fi
    }
    
    # Update service endpoints configuration
    # run_script /scripts/update-service-endpoints-configmap.sh
    
    
    # Configure MongoDB if enabled
    if [[ "$IS_MONGO_ENABLED" == "true" ]]; then
        echo "CONFIGURING MONGO..."
        run_script /scripts/mongo-service-init.sh
    else
        echo "MongoDB configuration is disabled."
    fi
    
    # Configure Postgres if enabled
    if [[ "$IS_PSQL_ENABLED" == "true" ]]; then
        echo "CONFIGURING POSTGRES..."
        run_script /scripts/psql-service-init.sh
    else
        echo "Postgres configuration is disabled."
    fi
    
    if [[ "$IS_S3_ENABLED" == "true" ]]; then
        echo "CONFIGURING S3 for the service..."
        run_script /scripts/s3-service-init.sh
    else
        echo "s3 configuration is disabled."
    fi
    echo "Configuration complete."
    

  create_dynamic_environment_variables.sh: |
    #!/bin/bash
    
    # Enable debugging if the environment variable is set
    if [[ "$SCRIPT_DEBUGGING_ENABLED" == "true" ]]; then
        echo "DEBUGGING ENABLED"
        set -x
    fi
    
    # Create the environment file if it does not exist
    ENV_FILE="/etc/additional_envs/.env"
    
    # Ensure directory exists
    mkdir -p "$(dirname "$ENV_FILE")"
    
    # Clear the environment file to start fresh
    > "$ENV_FILE"
    
    # Determine the role based on the pod ordinal number
    POD_NAME=$(hostname)
    POD_ORDINAL=$(echo $POD_NAME | awk -F '-' '{print $NF}')
    
    if [[ "$POD_ORDINAL" == "0" ]]; then
        echo "export SERVICE_INSTANCE_ROLE=MASTER" >> "$ENV_FILE"
    else
        echo "export SERVICE_INSTANCE_ROLE=SLAVE" >> "$ENV_FILE"
    fi
    
    # Write other environment variables if needed
    # echo "OTHER_ENV_VAR=some_value" >> "$ENV_FILE"
    
    
    # We want to mount the correct configmap to /opt/service-confs/
    ln -s /opt/service-confs/service_cluster_context_v2-pod-${POD_ORDINAL} /opt/service_cluster_context_v2.conf
    
    # Create directory for heapdumps
    mkdir -p /opt/heapdumps
    
    
    # Add additional environment variable configurations here as needed
---
# Source: microservice-commons-chart/templates/configmaps/service_endoints_config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: override-service-endpoints
  labels:
    helm.sh/chart: microservice-commons-chart-0.1.0
    app.kubernetes.io/name: microservice-commons-chart
    app.kubernetes.io/instance: commons
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
data:
  override-service-endpoints: |
    stage.kalpha.categories-service-url="http://kalpha-categories.stage-backend.svc.cluster.local"
    stage.kalpha.categories-service-admin-external-url="http://kalpha-categories.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-api-gateway-service-url="http://kalpha-n-api-gateway.stage-backend.svc.cluster.local"
    stage.kalpha.n-api-gateway-service-admin-external-url="http://kalpha-n-api-gateway.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-authentication-service-url="http://kalpha-n-authentication.stage-backend.svc.cluster.local"
    stage.kalpha.n-authentication-service-admin-external-url="http://kalpha-n-authentication.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-business-admin-web-service-url="http://kalpha-n-business-admin-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-business-admin-web-service-admin-external-url="http://kalpha-n-business-admin-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-core-cate-data-mediator-service-url="http://kalpha-n-core-cate-data-mediator.stage-backend.svc.cluster.local"
    stage.kalpha.n-core-cate-data-mediator-service-admin-external-url="http://kalpha-n-core-cate-data-mediator.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-data-integrations-service-url="http://kalpha-n-data-integrations.stage-backend.svc.cluster.local"
    stage.kalpha.n-data-integrations-service-admin-external-url="http://kalpha-n-data-integrations.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-data-seeding-service-url="http://kalpha-n-data-seeding.stage-backend.svc.cluster.local"
    stage.kalpha.n-data-seeding-service-admin-external-url="http://kalpha-n-data-seeding.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-device-registration-service-url="http://kalpha-n-device-registration.stage-backend.svc.cluster.local"
    stage.kalpha.n-device-registration-service-admin-external-url="http://kalpha-n-device-registration.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-event-hub-service-url="http://kalpha-n-event-hub.stage-backend.svc.cluster.local"
    stage.kalpha.n-event-hub-service-admin-external-url="http://kalpha-n-event-hub.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-fabric-inspection-service-url="http://kalpha-n-fabric-inspection.stage-backend.svc.cluster.local"
    stage.kalpha.n-fabric-inspection-service-admin-external-url="http://kalpha-n-fabric-inspection.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-factory-config-backend-service-url="http://kalpha-n-factory-config-backend.stage-backend.svc.cluster.local"
    stage.kalpha.n-factory-config-backend-service-admin-external-url="http://kalpha-n-factory-config-backend.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-factory-configuration-admin-service-url="http://kalpha-n-factory-configuration-admin.stage-backend.svc.cluster.local"
    stage.kalpha.n-factory-configuration-admin-service-admin-external-url="http://kalpha-n-factory-configuration-admin.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-flink-provision-service-url="http://kalpha-n-flink-provision.stage-backend.svc.cluster.local"
    stage.kalpha.n-flink-provision-service-admin-external-url="http://kalpha-n-flink-provision.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-generic-app-service-url="http://kalpha-n-generic-app.stage-backend.svc.cluster.local"
    stage.kalpha.n-generic-app-service-admin-external-url="http://kalpha-n-generic-app.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-input-app-web-service-url="http://kalpha-n-input-app-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-input-app-web-service-admin-external-url="http://kalpha-n-input-app-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-live-data-streaming-service-url="http://kalpha-n-live-data-streaming.stage-backend.svc.cluster.local"
    stage.kalpha.n-live-data-streaming-service-admin-external-url="http://kalpha-n-live-data-streaming.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-machine-maintenance-service-url="http://kalpha-n-machine-maintenance.stage-backend.svc.cluster.local"
    stage.kalpha.n-machine-maintenance-service-admin-external-url="http://kalpha-n-machine-maintenance.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-machine-maintenance-web-service-url="http://kalpha-n-machine-maintenance-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-machine-maintenance-web-service-admin-external-url="http://kalpha-n-machine-maintenance-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-mini-store-integration-service-url="http://kalpha-n-mini-store-integration.stage-backend.svc.cluster.local"
    stage.kalpha.n-mini-store-integration-service-admin-external-url="http://kalpha-n-mini-store-integration.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-notifications-service-url="http://kalpha-n-notifications.stage-backend.svc.cluster.local"
    stage.kalpha.n-notifications-service-admin-external-url="http://kalpha-n-notifications.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-oneapp-web-service-url="http://kalpha-n-oneapp-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-oneapp-web-service-admin-external-url="http://kalpha-n-oneapp-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-order-api-gateway-service-url="http://kalpha-n-order-api-gateway.stage-backend.svc.cluster.local"
    stage.kalpha.n-order-api-gateway-service-admin-external-url="http://kalpha-n-order-api-gateway.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-order-service-service-url="http://kalpha-n-order-service.stage-backend.svc.cluster.local"
    stage.kalpha.n-order-service-service-admin-external-url="http://kalpha-n-order-service.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-order-validation-service-url="http://kalpha-n-order-validation.stage-backend.svc.cluster.local"
    stage.kalpha.n-order-validation-service-admin-external-url="http://kalpha-n-order-validation.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-org-hierarchy-service-url="http://kalpha-n-org-hierarchy.stage-backend.svc.cluster.local"
    stage.kalpha.n-org-hierarchy-service-admin-external-url="http://kalpha-n-org-hierarchy.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-packing-tool-service-url="http://kalpha-n-packing-tool.stage-backend.svc.cluster.local"
    stage.kalpha.n-packing-tool-service-admin-external-url="http://kalpha-n-packing-tool.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-periodic-redis-sync-service-url="http://kalpha-n-periodic-redis-sync.stage-backend.svc.cluster.local"
    stage.kalpha.n-periodic-redis-sync-service-admin-external-url="http://kalpha-n-periodic-redis-sync.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-planning-tool-service-url="http://kalpha-n-planning-tool.stage-backend.svc.cluster.local"
    stage.kalpha.n-planning-tool-service-admin-external-url="http://kalpha-n-planning-tool.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-planning-tool-reports-service-url="http://kalpha-n-planning-tool-reports.stage-backend.svc.cluster.local"
    stage.kalpha.n-planning-tool-reports-service-admin-external-url="http://kalpha-n-planning-tool-reports.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-planning-tool-web-service-url="http://kalpha-n-planning-tool-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-planning-tool-web-service-admin-external-url="http://kalpha-n-planning-tool-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-remote-deployment-web-service-url="http://kalpha-n-remote-deployment-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-remote-deployment-web-service-admin-external-url="http://kalpha-n-remote-deployment-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-reports-backend-service-url="http://kalpha-n-reports-backend.stage-backend.svc.cluster.local"
    stage.kalpha.n-reports-backend-service-admin-external-url="http://kalpha-n-reports-backend.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-reports-oi-web-service-url="http://kalpha-n-reports-oi-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-reports-oi-web-service-admin-external-url="http://kalpha-n-reports-oi-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-reports-web-service-url="http://kalpha-n-reports-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-reports-web-service-admin-external-url="http://kalpha-n-reports-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-silhouettes-management-service-url="http://kalpha-n-silhouettes-management.stage-backend.svc.cluster.local"
    stage.kalpha.n-silhouettes-management-service-admin-external-url="http://kalpha-n-silhouettes-management.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-tags-management-service-url="http://kalpha-n-tags-management.stage-backend.svc.cluster.local"
    stage.kalpha.n-tags-management-service-admin-external-url="http://kalpha-n-tags-management.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-workflow-dashboard-web-service-url="http://kalpha-n-workflow-dashboard-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-workflow-dashboard-web-service-admin-external-url="http://kalpha-n-workflow-dashboard-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-workflow-data-integrations-service-url="http://kalpha-n-workflow-data-integrations.stage-backend.svc.cluster.local"
    stage.kalpha.n-workflow-data-integrations-service-admin-external-url="http://kalpha-n-workflow-data-integrations.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-workflow-reports-backend-service-url="http://kalpha-n-workflow-reports-backend.stage-backend.svc.cluster.local"
    stage.kalpha.n-workflow-reports-backend-service-admin-external-url="http://kalpha-n-workflow-reports-backend.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-workflow-reports-web-service-url="http://kalpha-n-workflow-reports-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-workflow-reports-web-service-admin-external-url="http://kalpha-n-workflow-reports-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-workflow-services-service-url="http://kalpha-n-workflow-services.stage-backend.svc.cluster.local"
    stage.kalpha.n-workflow-services-service-admin-external-url="http://kalpha-n-workflow-services.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-workflow-web-service-url="http://kalpha-n-workflow-web.stage-backend.svc.cluster.local"
    stage.kalpha.n-workflow-web-service-admin-external-url="http://kalpha-n-workflow-web.admin.stage-k8s.strawmine.com"
    stage.kalpha.n-workshift-management-service-url="http://kalpha-n-workshift-management.stage-backend.svc.cluster.local"
    stage.kalpha.n-workshift-management-service-admin-external-url="http://kalpha-n-workshift-management.admin.stage-k8s.strawmine.com"
    stage.kalpha.unified-id-service-url="http://kalpha-unified-id.stage-backend.svc.cluster.local"
    stage.kalpha.unified-id-service-admin-external-url="http://kalpha-unified-id.admin.stage-k8s.strawmine.com"
---
# Source: microservice-commons-chart/templates/configmaps/service_external_endpoints_config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: override-service-endpoints-external
  labels:
    helm.sh/chart: microservice-commons-chart-0.1.0
    app.kubernetes.io/name: microservice-commons-chart
    app.kubernetes.io/instance: commons
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
data:
  override-service-endpoints-external: |
      n-api-gateway-external-host-url="https://kalpha-n-api-gateway-external..stage-k8s.strawmine.com"
      n-business-admin-web-external-host-url="https://factoryadmin.nint.stage-k8s.strawmine.com"
      n-core-cate-data-mediator-external-host-url="https://kalpha-n-core-cate-data-mediator-external..stage-k8s.strawmine.com"
      n-factory-configuration-admin-external-host-url="https://kalpha-n-factory-configuration-admin-external..stage-k8s.strawmine.com"
      n-input-app-web-external-host-url="https://kalpha-n-input-app-web-external..stage-k8s.strawmine.com"
      n-live-data-streaming-external-host-url="https://kalpha-service-external..stage-k8s.strawmine.com"
      n-machine-maintenance-web-external-host-url="https://kalpha-n-machine-maintenance-web-external..stage-k8s.strawmine.com"
      n-oneapp-web-external-host-url="https://oneapp.kalpha.stage-k8s.strawmine.com"
      n-planning-tool-web-external-host-url="https://kalpha-n-planning-tool-web-external..stage-k8s.strawmine.com"
      n-reports-web-external-host-url="https://analytics.kalpha.stage-k8s.strawmine.com"
---
# Source: microservice-commons-chart/templates/roles/service-init-job-role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: backend-service-init-job-role
  labels:
    helm.sh/chart: microservice-commons-chart-0.1.0
    app.kubernetes.io/name: microservice-commons-chart
    app.kubernetes.io/instance: commons
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
  namespace: stage-backend
rules:
  - apiGroups: [""]
    resources: ["secrets","configmaps"]
    verbs: ["get", "create", "list"]
---
# Source: microservice-commons-chart/templates/roleBindings/service-init-job-role-binding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: backend-service-init-job-role-binding
  labels:
    helm.sh/chart: microservice-commons-chart-0.1.0
    app.kubernetes.io/name: microservice-commons-chart
    app.kubernetes.io/instance: commons
    app.kubernetes.io/version: "1.16.0"
    app.kubernetes.io/managed-by: Helm
  namespace: stage-backend
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: backend-service-init-job-role
subjects:
  - kind: ServiceAccount
    name: backend-service-init-job-sa
    namespace: stage-backend
