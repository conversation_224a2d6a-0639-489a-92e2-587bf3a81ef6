# EFS Heapdump Volume Setup Guide

## Overview

This guide explains how to set up EFS-based volumes for Java heapdumps in your Kubernetes backend services. The implementation provides pod-specific subdirectories within a shared EFS volume mounted at `/opt/heapdumps/<pod-name>`.

## What Was Added

### 1. Kubernetes Templates

- **StorageClass** (`templates/volumes/efs-storageclass.yaml`): Configures EFS CSI driver
- **PersistentVolume** (`templates/volumes/heapdump-pv.yaml`): Defines the EFS volume
- **PersistentVolumeClaim** (`templates/volumes/heapdump-pvc.yaml`): Claims the EFS volume

### 2. StatefulSet Modifications

- **Volume Mount**: Added conditional mount at `/opt/heapdumps`
- **Volume Definition**: Added EFS PVC reference
- **Directory Creation**: Automatic creation of pod-specific subdirectories
- **JVM Configuration**: Updated heap dump path to use pod-specific directories

### 3. Values Configuration

Added heapdump configuration to all service values files:

```yaml
heapdump:
  enabled: true
  storage: "2Gi"
  storageClassName: "efs-sc"
  efsFileSystemId: "fs-xxxxxxxxx"  # Replace with actual EFS file system ID
  efsPath: "/heapdumps"  # Optional: subdirectory within EFS
```

## Prerequisites

### 1. AWS EFS Setup

1. Create an EFS file system in your AWS account
2. Note the EFS file system ID (e.g., `fs-0123456789abcdef0`)
3. Create mount targets in your VPC subnets
4. Configure security groups to allow NFS traffic (port 2049)

### 2. EKS Cluster Setup

1. Install the AWS EFS CSI driver:
   ```bash
   kubectl apply -k "github.com/kubernetes-sigs/aws-efs-csi-driver/deploy/kubernetes/overlays/stable/?ref=release-1.7"
   ```

2. Create IAM role for EFS CSI driver (if not already done)

## Deployment Steps

### 1. Update Values File

Replace `fs-xxxxxxxxx` with your actual EFS file system ID in the values files:

```yaml
heapdump:
  enabled: true
  storage: "2Gi"
  storageClassName: "efs-sc"
  efsFileSystemId: "fs-0123456789abcdef0"  # Your actual EFS file system ID
  efsPath: "/heapdumps"
```

### 2. Deploy with Helm

```bash
helm upgrade --install <release-name> ./helmCharts/backend/backend-service-chart \
  -f ./helmCharts/backend/backend-service-chart/values/kalpha/<service-name>/values.yaml \
  --namespace <namespace>
```

### 3. Verify Deployment

1. Check if PV and PVC are created:
   ```bash
   kubectl get pv,pvc -n <namespace>
   ```

2. Check if pods have the volume mounted:
   ```bash
   kubectl describe pod <pod-name> -n <namespace>
   ```

3. Verify directory structure:
   ```bash
   kubectl exec -it <pod-name> -n <namespace> -- ls -la /opt/heapdumps/
   ```

## Directory Structure

```
/opt/heapdumps/
├── kalpha-n-authentication-0/
├── kalpha-n-authentication-1/
├── kalpha-n-data-integrations-0/
├── kalpha-n-machine-maintenance-0/
├── kalpha-n-machine-maintenance-1/
├── kalpha-n-machine-maintenance-2/
└── kalpha-n-live-data-streaming-0/
```

## JVM Configuration

The JVM is automatically configured with:
- `-XX:+HeapDumpOnOutOfMemoryError`: Enable heap dumps on OOM
- `-XX:HeapDumpPath=/opt/heapdumps/${POD_NAME}`: Pod-specific dump location
- `-XX:+ExitOnOutOfMemoryError`: Exit after creating heap dump

## Troubleshooting

### Common Issues

1. **PVC Pending**: Check if EFS file system ID is correct and EFS CSI driver is installed
2. **Mount Failures**: Verify security group rules allow NFS traffic
3. **Permission Denied**: Check if directories are created with proper permissions (777)

### Useful Commands

```bash
# Check EFS CSI driver pods
kubectl get pods -n kube-system | grep efs

# Check StorageClass
kubectl get storageclass efs-sc

# Check events for troubleshooting
kubectl get events -n <namespace> --sort-by='.lastTimestamp'
```

## Automated Cleanup Job

A CronJob is automatically deployed that runs every 15 minutes to:
- Scan for heap dump files in `/opt/heapdumps/<pod-name>/`
- Upload files to S3 at `s3://stage-k8s-heapdumps/<pod-name>/`
- Remove local files after successful upload verification

### Setup S3 and IAM

Run the setup script to create necessary AWS resources:

```bash
./helmCharts/backend/backend-service-chart/setup-heapdump-s3.sh
```

This creates:
- S3 bucket `stage-k8s-heapdumps`
- IAM role with S3 permissions
- Lifecycle policy (30-day retention)

### Configure IAM Role

Update your values files with the IAM role ARN:

```yaml
heapdump:
  enabled: true
  efsFileSystemId: "fs-0123456789abcdef0"
  s3Bucket: "stage-k8s-heapdumps"
  serviceAccount:
    roleArn: "arn:aws:iam::ACCOUNT_ID:role/EKS-HeapdumpCleanup-Role"
```

## Notes

- The EFS volume supports ReadWriteMany, allowing multiple pods to share the same volume
- Each pod gets its own subdirectory to prevent conflicts
- Heap dumps are automatically uploaded to S3 and cleaned up locally
- S3 lifecycle policy automatically deletes old versions after 30 days
- See `HEAPDUMP_CLEANUP.md` for detailed cleanup job documentation
